import React from 'react';
import { Message } from '../../types';
import { formatDistanceToNow } from '../../utils/dateUtils';

interface MessageItemProps {
  message: Message;
}

const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const isAssistant = message.sender === 'assistant';
  
  return (
    <div className={`flex ${isAssistant ? 'justify-start' : 'justify-end'} mb-4`}>
      {isAssistant && (
        <div className="h-8 w-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center shadow-glow-cyan mr-2">
          <span className="text-xs font-bold text-white">BAT</span>
        </div>
      )}
      
      <div className={`max-w-[80%] ${isAssistant ? 'order-2' : 'order-1'}`}>
        <div 
          className={`
            px-4 py-2 rounded-xl shadow-md
            ${isAssistant 
              ? 'bg-gray-800 border border-gray-700 text-gray-200' 
              : 'bg-gradient-to-r from-cyan-900/70 to-blue-900/70 text-white border border-cyan-800/50'}
          `}
        >
          <p>{message.content}</p>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          {formatDistanceToNow(message.timestamp)}
        </p>
      </div>
      
      {!isAssistant && (
        <div className="h-8 w-8 rounded-full bg-gray-700 flex items-center justify-center ml-2 order-2">
          <span className="text-xs font-bold text-white">T</span>
        </div>
      )}
    </div>
  );
};

export default MessageItem;