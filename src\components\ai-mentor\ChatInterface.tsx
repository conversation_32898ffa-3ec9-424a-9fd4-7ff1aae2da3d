import React, { useState, useRef, useEffect } from 'react';
import { Send, Mic, MicOff, Volume2, User, Bot } from 'lucide-react';
import { AIMessage } from '../../types';

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<AIMessage[]>([
    {
      id: '1',
      content: 'Hello! I\'m your AI learning assistant. How can I help you today?',
      sender: 'ai',
      timestamp: new Date().toISOString(),
      type: 'text'
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (inputMessage.trim() === '') return;

    const newUserMessage: AIMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: 'user',
      timestamp: new Date().toISOString(),
      type: 'text'
    };

    setMessages([...messages, newUserMessage]);
    setInputMessage('');

    // Simulate AI response after a short delay
    setTimeout(() => {
      const aiResponse: AIMessage = {
        id: (Date.now() + 1).toString(),
        content: getAIResponse(inputMessage),
        sender: 'ai',
        timestamp: new Date().toISOString(),
        type: 'text'
      };

      setMessages(prevMessages => [...prevMessages, aiResponse]);
    }, 1000);
  };

  const getAIResponse = (question: string): string => {
    // Simple response logic for demo purposes
    if (question.toLowerCase().includes('math') || question.toLowerCase().includes('mathematics')) {
      return "I can help with mathematics! What specific topic are you studying? Fractions, decimals, geometry, or something else?";
    } else if (question.toLowerCase().includes('science')) {
      return "Science is fascinating! Are you learning about plants, animals, the solar system, or perhaps simple machines?";
    } else if (question.toLowerCase().includes('hello') || question.toLowerCase().includes('hi')) {
      return "Hello! I'm your AI learning assistant. What subject are you studying today?";
    } else {
      return "That's an interesting question! Let me help you understand this topic better. Could you provide more details about what you're trying to learn?";
    }
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
    
    if (!isRecording) {
      // Simulate voice recording and response
      setTimeout(() => {
        const newUserMessage: AIMessage = {
          id: Date.now().toString(),
          content: "Can you explain photosynthesis?",
          sender: 'user',
          timestamp: new Date().toISOString(),
          type: 'voice'
        };
        
        setMessages([...messages, newUserMessage]);
        
        // Simulate AI response
        setTimeout(() => {
          const aiResponse: AIMessage = {
            id: (Date.now() + 1).toString(),
            content: "Photosynthesis is the process plants use to convert light energy into chemical energy. Plants take in carbon dioxide and water, and with the help of sunlight, transform these into oxygen and glucose. The glucose serves as food for the plant, while oxygen is released into the air for us to breathe.",
            sender: 'ai',
            timestamp: new Date().toISOString(),
            type: 'text'
          };
          
          setMessages(prevMessages => [...prevMessages, aiResponse]);
          setIsRecording(false);
        }, 1500);
      }, 2000);
    }
  };

  const playVoiceResponse = (message: AIMessage) => {
    // In a real app, this would use the Web Speech API
    console.log('Playing voice response:', message.content);
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="flex flex-col h-full bg-white rounded-lg border border-gray-200 shadow-soft overflow-hidden">
      <div className="bg-primary-50 p-4 border-b border-primary-100">
        <h2 className="text-lg font-medium text-primary-800">AI Learning Mentor</h2>
        <p className="text-sm text-primary-600">Ask any question about your studies</p>
      </div>
      
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="space-y-4">
          {messages.map((message) => (
            <div 
              key={message.id} 
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`flex items-start max-w-[80%] ${message.sender === 'user' ? 'flex-row-reverse' : ''}`}>
                <div className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${
                  message.sender === 'user' 
                    ? 'bg-primary-100 text-primary-700 ml-2' 
                    : 'bg-secondary-100 text-secondary-700 mr-2'
                }`}>
                  {message.sender === 'user' ? <User size={16} /> : <Bot size={16} />}
                </div>
                
                <div>
                  <div className={`p-3 rounded-lg ${
                    message.sender === 'user' 
                      ? 'bg-primary-500 text-white rounded-tr-none' 
                      : 'bg-gray-100 text-gray-800 rounded-tl-none'
                  }`}>
                    <p className="text-sm">{message.content}</p>
                    
                    {message.type === 'voice' && message.sender === 'ai' && (
                      <button 
                        onClick={() => playVoiceResponse(message)}
                        className="mt-2 text-xs flex items-center opacity-70 hover:opacity-100"
                      >
                        <Volume2 size={14} className="mr-1" />
                        Play audio
                      </button>
                    )}
                  </div>
                  
                  <div className={`mt-1 text-xs text-gray-500 ${message.sender === 'user' ? 'text-right' : ''}`}>
                    {formatTimestamp(message.timestamp)}
                    {message.type === 'voice' && (
                      <span className="ml-1 inline-flex items-center">
                        <Mic size={10} className="mr-0.5" />
                        Voice
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>
      
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center">
          <button 
            onClick={toggleRecording}
            className={`p-2 rounded-full mr-2 ${
              isRecording 
                ? 'bg-error-100 text-error-600 animate-pulse' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            aria-label={isRecording ? 'Stop recording' : 'Start recording'}
          >
            {isRecording ? <MicOff size={20} /> : <Mic size={20} />}
          </button>
          
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="Type your question here..."
            className="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          
          <button
            onClick={handleSendMessage}
            disabled={inputMessage.trim() === ''}
            className={`p-2 rounded-full ml-2 ${
              inputMessage.trim() === '' 
                ? 'bg-gray-100 text-gray-400' 
                : 'bg-primary-500 text-white hover:bg-primary-600'
            }`}
            aria-label="Send message"
          >
            <Send size={20} />
          </button>
        </div>
        
        {isRecording && (
          <div className="mt-2 text-sm text-center text-error-600">
            Listening... Speak clearly
          </div>
        )}
        
        <p className="mt-3 text-xs text-gray-500 text-center">
          This mentor works offline with limited capabilities. Connect to the internet for enhanced features.
        </p>
      </div>
    </div>
  );
};

export default ChatInterface;