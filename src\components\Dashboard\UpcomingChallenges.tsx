import React from 'react';
import { Calendar, Award } from 'lucide-react';
import { Challenge } from '../../types';

const UpcomingChallenges: React.FC = () => {
  const challenges: Challenge[] = [
    {
      id: '1',
      title: 'Build a Simple Circuit',
      description: 'Create a basic circuit using batteries and LED',
      type: 'craft',
      thumbnailUrl: 'https://images.pexels.com/photos/163100/circuit-circuit-board-resistor-computer-163100.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      deadline: '2025-06-15',
      reward: 'Science Kit',
      completionStatus: 'not-started'
    },
    {
      id: '2',
      title: 'Write a Story About Your Village',
      description: 'Create a short story about the history of your village',
      type: 'writing',
      thumbnailUrl: 'https://images.pexels.com/photos/733854/pexels-photo-733854.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      deadline: '2025-06-20',
      reward: 'Book Set',
      completionStatus: 'in-progress'
    }
  ];

  const formatDeadline = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  const getDaysRemaining = (dateString: string) => {
    const today = new Date();
    const deadline = new Date(dateString);
    const diffTime = deadline.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-soft overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-800">Upcoming Challenges</h3>
        <Award size={20} className="text-accent-500" />
      </div>
      
      <div className="p-6 divide-y divide-gray-200">
        {challenges.map((challenge) => {
          const daysRemaining = getDaysRemaining(challenge.deadline);
          
          return (
            <div key={challenge.id} className="py-4 first:pt-0 last:pb-0">
              <div className="flex items-start">
                <div className="h-14 w-14 rounded overflow-hidden flex-shrink-0">
                  <img 
                    src={challenge.thumbnailUrl} 
                    alt={challenge.title} 
                    className="h-full w-full object-cover"
                  />
                </div>
                
                <div className="ml-4 flex-1">
                  <h4 className="text-sm font-medium text-gray-900">{challenge.title}</h4>
                  <p className="text-xs text-gray-500 mt-1 line-clamp-2">{challenge.description}</p>
                  
                  <div className="mt-2 flex items-center text-xs">
                    <Calendar size={14} className="text-gray-400 mr-1" />
                    <span className="text-gray-500 mr-2">
                      Due: {formatDeadline(challenge.deadline)}
                    </span>
                    
                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                      daysRemaining <= 3 
                        ? 'bg-error-100 text-error-700' 
                        : daysRemaining <= 7 
                          ? 'bg-warning-100 text-warning-700' 
                          : 'bg-success-100 text-success-700'
                    }`}>
                      {daysRemaining} days left
                    </span>
                  </div>
                </div>
                
                <div className="ml-4">
                  <button className={`px-3 py-1 rounded-full text-xs font-medium ${
                    challenge.completionStatus === 'not-started'
                      ? 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'
                      : 'bg-primary-100 text-primary-700 hover:bg-primary-200'
                  }`}>
                    {challenge.completionStatus === 'not-started' ? 'Start' : 'Continue'}
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <button className="text-sm font-medium text-primary-600 hover:text-primary-700">
          View all challenges
        </button>
      </div>
    </div>
  );
};

export default UpcomingChallenges;