import React from 'react';
import * as LucideIcons from 'lucide-react';

interface NavItemProps {
  icon: string;
  label: string;
  active: boolean;
  onClick: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ icon, label, active, onClick }) => {
  // Dynamically get the icon component from lucide-react
  const IconComponent = (LucideIcons as Record<string, React.FC<{ size?: number; className?: string }>>)[icon] || LucideIcons.HelpCircle;
  
  return (
    <button
      onClick={onClick}
      className={`
        flex items-center w-full p-3 rounded-lg transition-all duration-200
        ${active 
          ? 'bg-gradient-to-r from-cyan-900/50 to-blue-900/30 text-cyan-400 border-l-2 border-cyan-400' 
          : 'text-gray-400 hover:bg-gray-800/50 hover:text-gray-200'}
      `}
    >
      <IconComponent size={20} className={active ? 'text-cyan-400' : 'text-gray-500'} />
      <span className="ml-3 font-medium">{label}</span>
      {active && (
        <span className="ml-auto">
          <div className="h-2 w-2 rounded-full bg-cyan-400 shadow-glow-cyan"></div>
        </span>
      )}
    </button>
  );
};

export default NavItem;