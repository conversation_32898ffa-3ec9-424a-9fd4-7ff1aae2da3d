import React from 'react';
import StatusWidgets from './StatusWidgets';
import SmartDevices from './SmartDevices';
import AppLauncher from './AppLauncher';
import { useAppContext } from '../../context/AppContext';
import Card from '../UI/Card';
import * as LucideIcons from 'lucide-react';

const DashboardView: React.FC = () => {
  const { userProfile, tasks } = useAppContext();
  
  // Get current time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };
  
  // Get next upcoming task
  const getNextTask = () => {
    const incompleteTasks = tasks.filter(task => !task.completed && task.dueDate);
    if (incompleteTasks.length === 0) return null;
    
    return incompleteTasks.sort((a, b) => {
      const dateA = a.dueDate ? new Date(a.dueDate).getTime() : Infinity;
      const dateB = b.dueDate ? new Date(b.dueDate).getTime() : Infinity;
      return dateA - dateB;
    })[0];
  };
  
  const nextTask = getNextTask();
  
  return (
    <div className="flex flex-col h-full overflow-auto p-4 space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="col-span-2 bg-gradient-to-r from-gray-900 to-gray-800">
          <div className="flex items-center">
            <div className="mr-4">
              <div className="h-16 w-16 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center shadow-glow-cyan">
                <span className="text-2xl font-bold text-white">
                  {userProfile.name.charAt(0)}
                </span>
              </div>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                {getGreeting()}, {userProfile.name}
              </h1>
              <p className="text-gray-400">
                {new Date().toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="bg-gradient-to-r from-gray-900 to-gray-800">
          <div className="flex flex-col h-full justify-center">
            <div className="text-4xl font-bold text-white mb-2 flex items-center">
              <LucideIcons.Clock className="mr-2 text-cyan-400" />
              {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
            {nextTask && (
              <div className="text-sm text-gray-400">
                <span className="text-cyan-400">Next:</span> {nextTask.title} at {nextTask.dueDate && new Date(nextTask.dueDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            )}
          </div>
        </Card>
      </div>
      
      <StatusWidgets />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <SmartDevices />
        <AppLauncher />
      </div>
    </div>
  );
};

export default DashboardView;