import React, { useState } from 'react';
import { Calendar, Check, X, Coffee } from 'lucide-react';
import { AttendanceRecord } from '../../types';

interface Student {
  id: string;
  name: string;
  grade: string;
  attendanceRate: number;
  mealsProvided: number;
}

const AttendanceTracker: React.FC = () => {
  const today = new Date().toISOString().split('T')[0];
  
  const [students, setStudents] = useState<Student[]>([
    { id: '1', name: '<PERSON><PERSON><PERSON>', grade: '5', attendanceRate: 90, mealsProvided: 35 },
    { id: '2', name: '<PERSON><PERSON> <PERSON>', grade: '5', attendanceRate: 95, mealsProvided: 36 },
    { id: '3', name: '<PERSON><PERSON>', grade: '5', attendanceRate: 85, mealsProvided: 30 },
    { id: '4', name: '<PERSON><PERSON>', grade: '5', attendanceRate: 92, mealsProvided: 34 },
    { id: '5', name: '<PERSON><PERSON><PERSON>', grade: '5', attendanceRate: 88, mealsProvided: 32 }
  ]);
  
  const [attendance, setAttendance] = useState<AttendanceRecord[]>(
    students.map(student => ({
      id: `att-${student.id}`,
      studentId: student.id,
      date: today,
      status: 'present',
      mealProvided: true,
      notes: ''
    }))
  );
  
  const [selectedDate, setSelectedDate] = useState<string>(today);
  
  const updateAttendance = (studentId: string, status: 'present' | 'absent' | 'late') => {
    setAttendance(prev => 
      prev.map(record => 
        record.studentId === studentId ? { ...record, status } : record
      )
    );
  };
  
  const updateMealProvided = (studentId: string) => {
    setAttendance(prev => 
      prev.map(record => 
        record.studentId === studentId ? { ...record, mealProvided: !record.mealProvided } : record
      )
    );
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <Check size={16} className="text-success-500" />;
      case 'absent':
        return <X size={16} className="text-error-500" />;
      case 'late':
        return <Calendar size={16} className="text-warning-500" />;
      default:
        return null;
    }
  };
  
  const saveAttendance = () => {
    // In a real app, this would sync with backend
    console.log('Saving attendance:', attendance);
    alert('Attendance saved successfully! Will sync when online.');
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-soft overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-800">Attendance & Meal Tracker</h3>
        <div className="flex items-center">
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="p-1 text-sm border border-gray-300 rounded"
          />
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Student
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Present
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Absent
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Late
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Meal
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {students.map((student) => {
              const studentAttendance = attendance.find(record => record.studentId === student.id);
              
              return (
                <tr key={student.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-700">
                        {student.name.charAt(0)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{student.name}</div>
                        <div className="text-xs text-gray-500">Grade {student.grade}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <button
                      onClick={() => updateAttendance(student.id, 'present')}
                      className={`inline-flex h-6 w-6 items-center justify-center rounded-full ${
                        studentAttendance?.status === 'present' 
                          ? 'bg-success-100 text-success-700 border-2 border-success-500' 
                          : 'bg-gray-100 text-gray-400 hover:bg-gray-200'
                      }`}
                      aria-label="Mark as present"
                    >
                      <Check size={14} />
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <button
                      onClick={() => updateAttendance(student.id, 'absent')}
                      className={`inline-flex h-6 w-6 items-center justify-center rounded-full ${
                        studentAttendance?.status === 'absent' 
                          ? 'bg-error-100 text-error-700 border-2 border-error-500' 
                          : 'bg-gray-100 text-gray-400 hover:bg-gray-200'
                      }`}
                      aria-label="Mark as absent"
                    >
                      <X size={14} />
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <button
                      onClick={() => updateAttendance(student.id, 'late')}
                      className={`inline-flex h-6 w-6 items-center justify-center rounded-full ${
                        studentAttendance?.status === 'late' 
                          ? 'bg-warning-100 text-warning-700 border-2 border-warning-500' 
                          : 'bg-gray-100 text-gray-400 hover:bg-gray-200'
                      }`}
                      aria-label="Mark as late"
                    >
                      <Calendar size={14} />
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <button
                      onClick={() => updateMealProvided(student.id)}
                      className={`inline-flex h-6 w-6 items-center justify-center rounded-full ${
                        studentAttendance?.mealProvided 
                          ? 'bg-accent-100 text-accent-700 border-2 border-accent-500' 
                          : 'bg-gray-100 text-gray-400 hover:bg-gray-200'
                      }`}
                      aria-label={studentAttendance?.mealProvided ? "Meal provided" : "No meal provided"}
                    >
                      <Coffee size={14} />
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
        <div className="text-sm text-gray-500">
          <span className="font-medium">{students.length}</span> students in Grade 5
        </div>
        <button
          onClick={saveAttendance}
          className="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        >
          Save Attendance
        </button>
      </div>
    </div>
  );
};

export default AttendanceTracker;