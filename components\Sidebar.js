import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { useAppContext } from '../context/AppContext';
import { ViewType } from '../types';
import { Feather } from '@expo/vector-icons';

const Sidebar = () => {
  const { activeView, setActiveView, userProfile } = useAppContext();
  
  const navItems = [
    { id: ViewType.DASHBOARD, label: 'Dashboard', icon: 'layout' },
    { id: ViewType.CHAT, label: 'Conversation', icon: 'message-circle' },
    { id: ViewType.TASKS, label: 'Tasks', icon: 'check-square' },
    { id: ViewType.FILES, label: 'Files', icon: 'folder' },
    { id: ViewType.SETTINGS, label: 'Settings', icon: 'settings' },
  ];
  
  return (
    <View style={styles.sidebar}>
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <View style={styles.logo}>
            <Feather name="zap" size={20} color="#FFFFFF" />
          </View>
          <Text style={styles.logoText}>BAT</Text>
        </View>
      </View>
      
      <ScrollView style={styles.nav}>
        {navItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.navItem,
              activeView === item.id && styles.navItemActive
            ]}
            onPress={() => setActiveView(item.id)}
          >
            <Feather
              name={item.icon}
              size={20}
              color={activeView === item.id ? '#0EF7F7' : '#6E6E8E'}
            />
            <Text
              style={[
                styles.navText,
                activeView === item.id && styles.navTextActive
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      <View style={styles.footer}>
        <View style={styles.userInfo}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>{userProfile.name.charAt(0)}</Text>
          </View>
          <View>
            <Text style={styles.userName}>{userProfile.name}</Text>
            <Text style={styles.userRole}>System Admin</Text>
          </View>
        </View>
        
        <TouchableOpacity style={styles.logoutButton}>
          <Feather name="log-out" size={18} color="#FFFFFF" />
          <Text style={styles.logoutText}>Log out</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  sidebar: {
    width: 80,
    backgroundColor: '#0A0A18',
    borderRightWidth: 1,
    borderRightColor: '#1D1D3B',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#1D1D3B',
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
  },
  logo: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: '#0BC5C5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  logoText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  nav: {
    flex: 1,
    padding: 8,
  },
  navItem: {
    flexDirection: 'column',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  navItemActive: {
    backgroundColor: '#12122A',
  },
  navText: {
    color: '#6E6E8E',
    fontSize: 12,
    marginTop: 4,
  },
  navTextActive: {
    color: '#0EF7F7',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#1D1D3B',
    alignItems: 'center',
  },
  userInfo: {
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1D1D3B',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  avatarText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  userName: {
    color: '#FFFFFF',
    fontSize: 14,
    textAlign: 'center',
  },
  userRole: {
    color: '#6E6E8E',
    fontSize: 12,
    textAlign: 'center',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#1D1D3B',
    width: '100%',
  },
  logoutText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontSize: 14,
  },
});

export default Sidebar;
