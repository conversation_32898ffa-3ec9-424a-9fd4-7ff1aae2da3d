import React from 'react';
import { useAppContext } from '../../context/AppContext';
import Sidebar from './Sidebar';
import Header from './Header';
import DashboardView from '../Dashboard/DashboardView';
import ChatInterface from '../Chat/ChatInterface';
import TaskList from '../Tasks/TaskList';
import FilesView from '../Files/FilesView';
import SettingsView from '../Settings/SettingsView';

const Layout: React.FC = () => {
  const { activeView, isAuthenticated } = useAppContext();

  // Render login screen if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <div className="text-center mb-8">
            <div className="h-20 w-20 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center shadow-glow-cyan mx-auto mb-4">
              <span className="text-2xl font-bold text-white">BAT</span>
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">Biometric Activated Terminal</h1>
            <p className="text-gray-400">Welcome to your personal AI assistant</p>
          </div>

          <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
            <button
              onClick={() => useAppContext().authenticate(true)}
              className="w-full bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-700 hover:to-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 shadow-lg"
            >
              Access Terminal
            </button>
          </div>
        </div>
      </div>
    );
  }

  const renderActiveView = () => {
    switch (activeView) {
      case 'dashboard':
        return <DashboardView />;
      case 'chat':
        return <ChatInterface />;
      case 'tasks':
        return <TaskList />;
      case 'files':
        return <FilesView />;
      case 'settings':
        return <SettingsView />;
      default:
        return <DashboardView />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-950">
      <Sidebar />
      <div className="flex flex-col flex-1 overflow-hidden">
        <Header />
        <main className="flex-1 overflow-hidden">
          {renderActiveView()}
        </main>
      </div>
    </div>
  );
};

export default Layout;