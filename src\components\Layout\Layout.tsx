import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';
import OfflineBanner from '../ui/OfflineBanner';
import SyncIndicator from '../ui/SyncIndicator';

const Layout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState({
    syncInProgress: false,
    pendingChanges: 0,
    lastSynced: localStorage.getItem('lastSynced') || null
  });

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Simulate sync status for demo
    if (isOnline && syncStatus.pendingChanges > 0) {
      setSyncStatus({
        syncInProgress: true,
        pendingChanges: syncStatus.pendingChanges,
        lastSynced: syncStatus.lastSynced
      });

      const timer = setTimeout(() => {
        const now = new Date().toISOString();
        localStorage.setItem('lastSynced', now);
        setSyncStatus({
          syncInProgress: false,
          pendingChanges: 0,
          lastSynced: now
        });
      }, 2000);

      return () => clearTimeout(timer);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isOnline, syncStatus.pendingChanges]);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const closeSidebarOnMobile = () => {
    if (window.innerWidth < 1024) {
      setSidebarOpen(false);
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} isOnline={isOnline} />
      
      {/* Overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-10 bg-gray-900 bg-opacity-50 lg:hidden"
          onClick={toggleSidebar}
        ></div>
      )}
      
      <div className="flex flex-col flex-1 min-h-screen overflow-x-hidden">
        <Header toggleSidebar={toggleSidebar} sidebarOpen={sidebarOpen} />
        
        {!isOnline && <OfflineBanner pendingChanges={syncStatus.pendingChanges} />}
        
        {isOnline && syncStatus.syncInProgress && (
          <SyncIndicator pendingChanges={syncStatus.pendingChanges} />
        )}
        
        <main className="flex-1 p-4 sm:p-6 overflow-y-auto" onClick={closeSidebarOnMobile}>
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default Layout;