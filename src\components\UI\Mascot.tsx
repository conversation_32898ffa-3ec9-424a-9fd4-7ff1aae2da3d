import React from 'react';
import { Volume2 } from 'lucide-react';

interface MascotProps {
  message?: string;
  emotion?: 'happy' | 'thinking' | 'excited';
  onSpeak?: () => void;
}

const Mascot: React.FC<MascotProps> = ({ 
  message = "Hi! I'm <PERSON><PERSON>, your learning buddy! 🌟",
  emotion = 'happy',
  onSpeak 
}) => {
  const mascotImage = `https://images.pexels.com/photos/2405242/pexels-photo-2405242.jpeg?auto=compress&cs=tinysrgb&w=300`;

  return (
    <div className="fixed bottom-4 right-4 flex items-end">
      <div className="mr-4 mb-16 max-w-xs">
        {message && (
          <div className="relative bg-white p-4 rounded-2xl shadow-fun mb-4">
            <p className="font-comic text-lg">{message}</p>
            {onSpeak && (
              <button
                onClick={onSpeak}
                className="absolute top-2 right-2 p-2 text-fun-500 hover:text-fun-600 transition-colors"
                aria-label="Listen to message"
              >
                <Volume2 size={20} />
              </button>
            )}
            <div className="absolute -bottom-4 right-4 w-0 h-0 border-l-[8px] border-l-transparent border-t-[16px] border-white border-r-[8px] border-r-transparent" />
          </div>
        )}
      </div>
      <div className="relative w-32 h-32 animate-float">
        <img
          src={mascotImage}
          alt="Vika the Learning Assistant"
          className="w-full h-full object-cover rounded-full border-4 border-fun-500 shadow-fun"
        />
        <div className="absolute -top-2 -right-2 w-8 h-8 bg-candy-400 rounded-full flex items-center justify-center text-white font-bold shadow-fun">
          ?
        </div>
      </div>
    </div>
  );
};

export default Mascot;