import React from 'react';
import { Task } from '../../types';
import * as LucideIcons from 'lucide-react';
import Badge from '../UI/Badge';

interface TaskItemProps {
  task: Task;
  toggleComplete: (id: string) => void;
}

const TaskItem: React.FC<TaskItemProps> = ({ task, toggleComplete }) => {
  const priorityVariants = {
    low: 'info',
    medium: 'warning',
    high: 'error',
  };
  
  return (
    <div 
      className={`
        p-3 mb-2 rounded-lg border border-gray-800 transition-all
        ${task.completed 
          ? 'bg-gray-900/30 opacity-70' 
          : 'bg-gray-800/70'}
      `}
    >
      <div className="flex items-center">
        <button
          onClick={() => toggleComplete(task.id)}
          className={`
            h-6 w-6 rounded-full border mr-3 flex items-center justify-center
            ${task.completed 
              ? 'bg-green-900/30 border-green-500 text-green-500' 
              : 'border-gray-600 text-transparent hover:border-cyan-500'}
          `}
        >
          {task.completed && <LucideIcons.Check size={14} />}
        </button>
        
        <div className="flex-1">
          <p className={`font-medium ${task.completed ? 'line-through text-gray-500' : 'text-white'}`}>
            {task.title}
          </p>
          
          <div className="flex items-center mt-1 text-xs text-gray-400">
            {task.category && (
              <span className="mr-3">{task.category}</span>
            )}
            
            {task.dueDate && (
              <span className="flex items-center">
                <LucideIcons.Calendar size={12} className="mr-1" />
                {new Date(task.dueDate).toLocaleDateString()}
              </span>
            )}
          </div>
        </div>
        
        <Badge variant={priorityVariants[task.priority] as 'default' | 'success' | 'warning' | 'error' | 'info'}>
          {task.priority}
        </Badge>
      </div>
    </div>
  );
};

export default TaskItem;