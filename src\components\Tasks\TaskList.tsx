import React, { useState } from 'react';
import { useAppContext } from '../../context/AppContext';
import TaskItem from './TaskItem';
import Button from '../UI/Button';
import * as LucideIcons from 'lucide-react';

const TaskList: React.FC = () => {
  const { tasks, toggleTaskCompletion } = useAppContext();
  const [filter, setFilter] = useState<'all' | 'active' | 'completed'>('all');
  
  const filteredTasks = tasks.filter((task) => {
    if (filter === 'all') return true;
    if (filter === 'active') return !task.completed;
    if (filter === 'completed') return task.completed;
    return true;
  });
  
  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-800 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-cyan-400">
          <LucideIcons.CheckSquare className="inline mr-2" size={20} />
          Tasks
        </h2>
        <div className="flex space-x-2">
          <Button 
            variant={filter === 'all' ? 'primary' : 'ghost'} 
            size="sm" 
            onClick={() => setFilter('all')}
          >
            All
          </Button>
          <Button 
            variant={filter === 'active' ? 'primary' : 'ghost'} 
            size="sm" 
            onClick={() => setFilter('active')}
          >
            Active
          </Button>
          <Button 
            variant={filter === 'completed' ? 'primary' : 'ghost'} 
            size="sm" 
            onClick={() => setFilter('completed')}
          >
            Completed
          </Button>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-900">
        {filteredTasks.length > 0 ? (
          filteredTasks.map((task) => (
            <TaskItem
              key={task.id}
              task={task}
              toggleComplete={toggleTaskCompletion}
            />
          ))
        ) : (
          <div className="text-center py-10 text-gray-500">
            <LucideIcons.CheckSquare size={40} className="mx-auto mb-2 opacity-30" />
            <p>No {filter !== 'all' ? filter : ''} tasks found</p>
          </div>
        )}
      </div>
      
      <div className="p-4 border-t border-gray-800">
        <Button variant="primary" className="w-full">
          <LucideIcons.Plus size={16} className="mr-2" />
          Add New Task
        </Button>
      </div>
    </div>
  );
};

export default TaskList;