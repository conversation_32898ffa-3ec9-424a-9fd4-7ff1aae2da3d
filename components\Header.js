import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { useAppContext } from '../context/AppContext';
import { Feather } from '@expo/vector-icons';

const Header = () => {
  const { isOfflineMode, toggleOfflineMode } = useAppContext();
  
  // Get current date
  const getCurrentDate = () => {
    return new Date().toLocaleDateString('en-US', { 
      weekday: 'long', 
      month: 'long', 
      day: 'numeric' 
    });
  };
  
  return (
    <View style={styles.header}>
      <Text style={styles.date}>{getCurrentDate()}</Text>
      
      <View style={styles.actions}>
        <TouchableOpacity 
          style={[
            styles.statusButton,
            isOfflineMode ? styles.statusButtonOffline : styles.statusButtonOnline
          ]}
          onPress={toggleOfflineMode}
        >
          <Feather 
            name="wifi" 
            size={14} 
            color={isOfflineMode ? '#FF4D4D' : '#4DFF88'} 
          />
          <Text 
            style={[
              styles.statusText,
              isOfflineMode ? styles.statusTextOffline : styles.statusTextOnline
            ]}
          >
            {isOfflineMode ? 'Offline' : 'Online'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.iconButton}>
          <Feather name="bell" size={20} color="#FFFFFF" />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.iconButton}>
          <Feather name="search" size={20} color="#FFFFFF" />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.iconButton}>
          <Feather name="help-circle" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    height: 60,
    backgroundColor: '#0A0A18',
    borderBottomWidth: 1,
    borderBottomColor: '#1D1D3B',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  date: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 12,
    borderWidth: 1,
  },
  statusButtonOnline: {
    backgroundColor: 'rgba(77, 255, 136, 0.1)',
    borderColor: 'rgba(77, 255, 136, 0.3)',
  },
  statusButtonOffline: {
    backgroundColor: 'rgba(255, 77, 77, 0.1)',
    borderColor: 'rgba(255, 77, 77, 0.3)',
  },
  statusText: {
    fontSize: 12,
    marginLeft: 6,
  },
  statusTextOnline: {
    color: '#4DFF88',
  },
  statusTextOffline: {
    color: '#FF4D4D',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#12122A',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
});

export default Header;
