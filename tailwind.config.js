/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        math: {
          50: '#e6f3ff',
          100: '#cce7ff',
          200: '#99cfff',
          300: '#66b7ff',
          400: '#339fff',
          500: '#0087ff',
          600: '#006ccc',
          700: '#005199',
          800: '#003666',
          900: '#001b33',
        },
        science: {
          50: '#e8f7e8',
          100: '#d1efd1',
          200: '#a3dfa3',
          300: '#75cf75',
          400: '#47bf47',
          500: '#19af19',
          600: '#148c14',
          700: '#0f690f',
          800: '#0a460a',
          900: '#052305',
        },
        fun: {
          50: '#fff2e6',
          100: '#ffe5cc',
          200: '#ffcb99',
          300: '#ffb166',
          400: '#ff9733',
          500: '#ff7d00',
          600: '#cc6400',
          700: '#994b00',
          800: '#663200',
          900: '#331900',
        },
        candy: {
          50: '#fce4ff',
          100: '#f9c9ff',
          200: '#f393ff',
          300: '#ed5dff',
          400: '#e727ff',
          500: '#d900f5',
          600: '#ae00c4',
          700: '#820093',
          800: '#570062',
          900: '#2b0031',
        }
      },
      fontFamily: {
        comic: ['Comic Neue', 'cursive'],
        display: ['Poppins', 'sans-serif'],
      },
      boxShadow: {
        'fun': '4px 4px 0px rgba(0, 0, 0, 0.2)',
        'fun-lg': '8px 8px 0px rgba(0, 0, 0, 0.2)',
      },
      animation: {
        'bounce-slow': 'bounce 3s infinite',
        'float': 'float 6s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' },
        }
      }
    },
  },
  plugins: [],
};