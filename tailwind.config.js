/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        cyan: {
          400: '#0EF7F7',
          500: '#0BC5C5',
          600: '#09A3A3',
          700: '#078282',
          800: '#056060',
          900: '#034545',
        },
        gray: {
          900: '#0A0A18',
          800: '#12122A',
          700: '#1D1D3B',
          600: '#2A2A4C',
          500: '#3E3E5E',
          400: '#6E6E8E',
          300: '#9E9EBE',
        },
      },
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
};