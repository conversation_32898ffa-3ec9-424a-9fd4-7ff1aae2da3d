import React from 'react';
import { DivideIcon as LucideIcon } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  change?: {
    value: string | number;
    positive: boolean;
  };
  color: 'primary' | 'secondary' | 'accent' | 'success' | 'warning';
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, change, color }) => {
  const getColorClasses = () => {
    switch (color) {
      case 'primary':
        return 'bg-primary-50 text-primary-500 border-primary-200';
      case 'secondary':
        return 'bg-secondary-50 text-secondary-500 border-secondary-200';
      case 'accent':
        return 'bg-accent-50 text-accent-500 border-accent-200';
      case 'success':
        return 'bg-success-50 text-success-500 border-success-200';
      case 'warning':
        return 'bg-warning-50 text-warning-500 border-warning-200';
      default:
        return 'bg-primary-50 text-primary-500 border-primary-200';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-soft p-5">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <p className="mt-1 text-2xl font-semibold text-gray-900">{value}</p>
          
          {change && (
            <div className="mt-1 flex items-center">
              <span className={`text-xs font-medium ${change.positive ? 'text-success-600' : 'text-error-600'}`}>
                {change.positive ? '+' : ''}{change.value}
              </span>
            </div>
          )}
        </div>
        
        <div className={`p-3 rounded-full ${getColorClasses()}`}>
          {icon}
        </div>
      </div>
    </div>
  );
};

export default StatCard;