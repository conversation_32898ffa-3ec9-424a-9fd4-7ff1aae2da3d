import React from 'react';
import { useAppContext } from '../../context/AppContext';
import Card from '../UI/Card';
import SkillItem from './SkillItem';
import * as LucideIcons from 'lucide-react';
import Button from '../UI/Button';

const SettingsView: React.FC = () => {
  const { 
    userProfile,  
    skills, 
    toggleSkill, 
    isOfflineMode, 
    toggleOfflineMode 
  } = useAppContext();
  
  // Group skills by category
  const skillsByCategory = skills.reduce((acc, skill) => {
    const category = skill.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(skill);
    return acc;
  }, {} as Record<string, typeof skills>);
  
  return (
    <div className="flex flex-col h-full overflow-auto p-4 space-y-6">
      <Card title="User Profile" className="bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="flex items-center">
          <div className="mr-4">
            <div className="h-20 w-20 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center shadow-glow-cyan">
              <span className="text-2xl font-bold text-white">
                {userProfile.name.charAt(0)}
              </span>
            </div>
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">{userProfile.name}</h1>
            <p className="text-gray-400">System Administrator</p>
            <Button variant="ghost" size="sm" className="mt-2">
              <LucideIcons.Edit size={14} className="mr-1" />
              Edit Profile
            </Button>
          </div>
        </div>
      </Card>
      
      <Card title="System Settings">
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 rounded-lg border border-gray-800 bg-gray-800/30">
            <div className="flex items-center">
              <div className="p-2 rounded-full mr-3 bg-gray-800 text-gray-500">
                <LucideIcons.Moon size={20} />
              </div>
              <div>
                <p className="font-medium text-white">Dark Mode</p>
                <p className="text-xs text-gray-400">Enable dark mode interface</p>
              </div>
            </div>
            <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-cyan-600">
              <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
            </button>
          </div>
          
          <div className="flex items-center justify-between p-3 rounded-lg border border-gray-800 bg-gray-800/30">
            <div className="flex items-center">
              <div className="p-2 rounded-full mr-3 bg-gray-800 text-gray-500">
                <LucideIcons.Volume2 size={20} />
              </div>
              <div>
                <p className="font-medium text-white">Voice Assistant</p>
                <p className="text-xs text-gray-400">Enable voice responses</p>
              </div>
            </div>
            <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-cyan-600">
              <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
            </button>
          </div>
          
          <div className="flex items-center justify-between p-3 rounded-lg border border-gray-800 bg-gray-800/30">
            <div className="flex items-center">
              <div className="p-2 rounded-full mr-3 bg-gray-800 text-gray-500">
                <LucideIcons.Wifi size={20} />
              </div>
              <div>
                <p className="font-medium text-white">Offline Mode</p>
                <p className="text-xs text-gray-400">Use only local functionality</p>
              </div>
            </div>
            <button 
              onClick={toggleOfflineMode}
              className={`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${isOfflineMode ? 'bg-cyan-600' : 'bg-gray-700'}
              `}
            >
              <span
                className={`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${isOfflineMode ? 'translate-x-6' : 'translate-x-1'}
                `}
              />
            </button>
          </div>
          
          <div className="flex items-center justify-between p-3 rounded-lg border border-gray-800 bg-gray-800/30">
            <div className="flex items-center">
              <div className="p-2 rounded-full mr-3 bg-gray-800 text-gray-500">
                <LucideIcons.Bell size={20} />
              </div>
              <div>
                <p className="font-medium text-white">Notifications</p>
                <p className="text-xs text-gray-400">Enable system notifications</p>
              </div>
            </div>
            <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-cyan-600">
              <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
            </button>
          </div>
        </div>
      </Card>
      
      <Card title="Available Skills">
        <div className="space-y-6">
          {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
            <div key={category}>
              <h3 className="text-sm font-semibold text-gray-400 uppercase mb-3">
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </h3>
              <div className="space-y-2">
                {categorySkills.map((skill) => (
                  <SkillItem
                    key={skill.id}
                    skill={skill}
                    onToggle={toggleSkill}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      </Card>
      
      <Card title="Security & Privacy">
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 rounded-lg border border-gray-800 bg-gray-800/30">
            <div className="flex items-center">
              <div className="p-2 rounded-full mr-3 bg-gray-800 text-gray-500">
                <LucideIcons.Shield size={20} />
              </div>
              <div>
                <p className="font-medium text-white">Biometric Authentication</p>
                <p className="text-xs text-gray-400">Use fingerprint or face ID</p>
              </div>
            </div>
            <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-cyan-600">
              <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
            </button>
          </div>
          
          <div className="flex items-center justify-between p-3 rounded-lg border border-gray-800 bg-gray-800/30">
            <div className="flex items-center">
              <div className="p-2 rounded-full mr-3 bg-gray-800 text-gray-500">
                <LucideIcons.Lock size={20} />
              </div>
              <div>
                <p className="font-medium text-white">Data Encryption</p>
                <p className="text-xs text-gray-400">Encrypt all local data</p>
              </div>
            </div>
            <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-cyan-600">
              <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
            </button>
          </div>
          
          <Button variant="danger" className="w-full mt-4">
            <LucideIcons.Trash2 size={16} className="mr-2" />
            Clear All Data
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default SettingsView;