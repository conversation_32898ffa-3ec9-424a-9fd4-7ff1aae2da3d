import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  onClick,
  className = '',
  disabled = false,
}) => {
  const baseStyle = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-gray-900';
  
  const variantStyles = {
    primary: 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white shadow-glow-cyan hover:from-cyan-600 hover:to-blue-600',
    secondary: 'bg-gray-800 text-cyan-400 border border-cyan-700 hover:bg-gray-700',
    ghost: 'bg-transparent text-cyan-400 hover:bg-gray-800 hover:text-cyan-300',
    danger: 'bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-glow-pink hover:from-red-600 hover:to-pink-600',
  };
  
  const sizeStyles = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-4 py-2',
    lg: 'text-base px-6 py-3',
  };
  
  const disabledStyle = disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseStyle} ${variantStyles[variant]} ${sizeStyles[size]} ${disabledStyle} ${className}`}
    >
      {icon && <span className="mr-2">{icon}</span>}
      {children}
    </button>
  );
};

export default Button;