import React from 'react';
import { useAppContext } from '../../context/AppContext';
import * as LucideIcons from 'lucide-react';
import Button from '../UI/Button';

const Header: React.FC = () => {
  const { isOfflineMode, toggleOfflineMode } = useAppContext();
  
  return (
    <header className="h-16 bg-gray-900/80 backdrop-blur-sm border-b border-gray-800 flex items-center justify-between px-4">
      <div>
        <h1 className="text-xl font-bold text-white">
          {new Date().toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
        </h1>
      </div>
      
      <div className="flex items-center space-x-3">
        <button 
          onClick={toggleOfflineMode}
          className={`
            flex items-center space-x-1 px-3 py-1 rounded-full border
            ${isOfflineMode 
              ? 'bg-red-900/20 border-red-700/50 text-red-400' 
              : 'bg-green-900/20 border-green-700/50 text-green-400'}
          `}
        >
          <LucideIcons.Wifi size={14} />
          <span className="text-xs font-medium">
            {isOfflineMode ? 'Offline' : 'Online'}
          </span>
        </button>
        
        <Button variant="ghost" size="sm">
          <LucideIcons.Bell size={16} />
        </Button>
        
        <Button variant="ghost" size="sm">
          <LucideIcons.Search size={16} />
        </Button>
        
        <Button variant="ghost" size="sm">
          <LucideIcons.HelpCircle size={16} />
        </Button>
      </div>
    </header>
  );
};

export default Header;