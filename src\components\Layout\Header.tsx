import React, { useState, useEffect } from 'react';
import { Menu, X, WifiOff, Wifi } from 'lucide-react';
import { useLocation } from 'react-router-dom';
import Logo from '../ui/Logo';

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean;
}

const Header: React.FC<HeaderProps> = ({ toggleSidebar, sidebarOpen }) => {
  const location = useLocation();
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const [lastSynced, setLastSynced] = useState<string | null>(null);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Simulate last synced time
    if (isOnline) {
      setLastSynced(new Date().toISOString());
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isOnline]);

  const getPageTitle = () => {
    const path = location.pathname;
    
    if (path === '/') return 'Dashboard';
    if (path.startsWith('/learning')) return 'Learning Modules';
    if (path.startsWith('/ai-mentor')) return 'AI Mentor';
    if (path.startsWith('/parent-connect')) return 'Parent Connect';
    if (path.startsWith('/attendance')) return 'Attendance & Meals';
    if (path.startsWith('/challenges')) return 'Challenges';
    if (path.startsWith('/resources')) return 'Teacher Resources';
    if (path.startsWith('/community')) return 'Community Wi-Fi';
    if (path.startsWith('/profile')) return 'Profile';
    if (path.startsWith('/settings')) return 'Settings';
    
    return 'Vikalpa';
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
      <div className="flex justify-between items-center px-4 py-3">
        <div className="flex items-center">
          <button 
            onClick={toggleSidebar}
            className="p-2 rounded-md text-gray-500 hover:bg-primary-50 hover:text-primary-500 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 lg:hidden"
            aria-label={sidebarOpen ? "Close menu" : "Open menu"}
          >
            {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
          </button>
          <div className="ml-2 lg:ml-0">
            <Logo compact />
          </div>
          <h1 className="ml-3 text-lg font-medium text-gray-800 hidden sm:block">
            {getPageTitle()}
          </h1>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="flex items-center">
            {isOnline ? (
              <div className="flex items-center text-success-600">
                <Wifi size={16} className="mr-1" />
                <span className="text-xs hidden sm:inline">
                  {lastSynced ? `Synced: ${new Date(lastSynced).toLocaleTimeString()}` : 'Online'}
                </span>
              </div>
            ) : (
              <div className="flex items-center text-warning-600">
                <WifiOff size={16} className="mr-1" />
                <span className="text-xs hidden sm:inline">Offline Mode</span>
              </div>
            )}
          </div>
          
          <div className="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 font-medium text-sm">
            US
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;