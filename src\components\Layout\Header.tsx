import React from 'react';
import { useAppContext } from '../../context/AppContext';
import * as LucideIcons from 'lucide-react';

const Header: React.FC = () => {
  const { isOfflineMode, toggleOfflineMode, activeView } = useAppContext();

  // Get current date and time
  const getCurrentDate = () => {
    return new Date().toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    });
  };

  const getCurrentTime = () => {
    return new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getPageTitle = () => {
    switch (activeView) {
      case 'dashboard':
        return 'Dashboard';
      case 'chat':
        return 'Conversation';
      case 'tasks':
        return 'Tasks';
      case 'files':
        return 'Files';
      case 'settings':
        return 'Settings';
      default:
        return 'Dashboard';
    }
  };

  return (
    <header className="h-16 bg-gray-900 border-b border-gray-800 flex items-center justify-between px-6">
      <div className="flex items-center">
        <h1 className="text-xl font-semibold text-white">{getPageTitle()}</h1>
        <div className="ml-6 text-sm text-gray-400">
          {getCurrentDate()} • {getCurrentTime()}
        </div>
      </div>

      <div className="flex items-center space-x-4">
        {/* Network Status */}
        <button
          onClick={toggleOfflineMode}
          className={`
            flex items-center px-3 py-1.5 rounded-full text-xs font-medium transition-colors
            ${isOfflineMode
              ? 'bg-red-900/30 text-red-400 border border-red-800/50'
              : 'bg-green-900/30 text-green-400 border border-green-800/50'
            }
          `}
        >
          <LucideIcons.Wifi size={14} className="mr-1.5" />
          {isOfflineMode ? 'Offline' : 'Online'}
        </button>

        {/* Quick Actions */}
        <button className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 text-gray-400 hover:text-white transition-colors">
          <LucideIcons.Bell size={18} />
        </button>

        <button className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 text-gray-400 hover:text-white transition-colors">
          <LucideIcons.Search size={18} />
        </button>

        <button className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 text-gray-400 hover:text-white transition-colors">
          <LucideIcons.HelpCircle size={18} />
        </button>
      </div>
    </header>
  );
};

export default Header;