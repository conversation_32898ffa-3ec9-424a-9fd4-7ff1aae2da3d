import React from 'react';
import Card from '../UI/Card';
import * as LucideIcons from 'lucide-react';
import { useAppContext } from '../../context/AppContext';

const StatusWidgets: React.FC = () => {
  const { tasks, smartDevices, isOfflineMode, isListening } = useAppContext();
  
  const activeTasks = tasks.filter((task) => !task.completed).length;
  const activeDevices = smartDevices.filter((device) => device.status === 'on').length;
  
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Card className="bg-gradient-to-br from-gray-900 to-gray-800">
        <div className="flex flex-col items-center">
          <div className="mb-2 text-cyan-400">
            <LucideIcons.CheckSquare size={24} />
          </div>
          <p className="text-2xl font-bold text-white mb-1">{activeTasks}</p>
          <p className="text-xs text-gray-400">Active Tasks</p>
        </div>
      </Card>
      
      <Card className="bg-gradient-to-br from-gray-900 to-gray-800">
        <div className="flex flex-col items-center">
          <div className="mb-2 text-cyan-400">
            <LucideIcons.Zap size={24} />
          </div>
          <p className="text-2xl font-bold text-white mb-1">{activeDevices}</p>
          <p className="text-xs text-gray-400">Devices Online</p>
        </div>
      </Card>
      
      <Card className="bg-gradient-to-br from-gray-900 to-gray-800">
        <div className="flex flex-col items-center">
          <div className="mb-2 text-cyan-400">
            <LucideIcons.Wifi size={24} className={isOfflineMode ? 'text-red-400' : ''} />
          </div>
          <p className="text-2xl font-bold text-white mb-1">
            {isOfflineMode ? 'Offline' : 'Online'}
          </p>
          <p className="text-xs text-gray-400">Network Status</p>
        </div>
      </Card>
      
      <Card className="bg-gradient-to-br from-gray-900 to-gray-800">
        <div className="flex flex-col items-center">
          <div className="mb-2 text-cyan-400">
            <LucideIcons.Mic size={24} className={isListening ? 'text-green-400 animate-pulse' : ''} />
          </div>
          <p className="text-2xl font-bold text-white mb-1">
            {isListening ? 'Listening' : 'Ready'}
          </p>
          <p className="text-xs text-gray-400">Voice Status</p>
        </div>
      </Card>
    </div>
  );
};

export default StatusWidgets;