@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
body {
  @apply bg-black text-white;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Cyberpunk Background */
.cyberpunk-bg {
  background-color: #0a0a18;
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(14, 247, 247, 0.03) 0%, transparent 30%),
    radial-gradient(circle at 90% 80%, rgba(246, 55, 236, 0.03) 0%, transparent 30%),
    linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(8, 8, 24, 0.9) 100%);
  background-attachment: fixed;
}

/* Scrollbar */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  @apply bg-gray-700 rounded-full;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-600;
}

/* Shadow Glow Effects */
.shadow-glow-cyan {
  box-shadow: 0 0 15px rgba(14, 247, 247, 0.3);
}

.shadow-glow-pink {
  box-shadow: 0 0 15px rgba(246, 55, 236, 0.3);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

/* Grid Lines Overlay (optional, can be activated by adding class) */
.grid-lines {
  background-image: linear-gradient(rgba(14, 247, 247, 0.05) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(14, 247, 247, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Custom focus ring */
.focus\:ring-offset-gray-900:focus {
  --tw-ring-offset-color: #171717;
}