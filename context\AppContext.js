import React, { createContext, useContext, useState, useEffect } from 'react';
import { generateDummyData } from '../utils/dummyData';
import { MessageType, ThemeType, ViewType } from '../types';

// Create the context
const AppContext = createContext();

// Provider component
export const AppProvider = ({ children }) => {
  const [messages, setMessages] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [smartDevices, setSmartDevices] = useState([]);
  const [appShortcuts, setAppShortcuts] = useState([]);
  const [userProfile, setUserProfile] = useState({
    name: 'Tony',
    preferences: {
      theme: ThemeType.DARK,
      voiceAssistant: true,
      notifications: true,
    },
  });
  const [skills, setSkills] = useState([]);
  const [activeView, setActiveView] = useState(ViewType.DASHBOARD);
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(true);
  const [isOfflineMode, setIsOfflineMode] = useState(false);

  // Initialize with dummy data
  useEffect(() => {
    const data = generateDummyData();
    setMessages(data.messages);
    setTasks(data.tasks);
    setSmartDevices(data.smartDevices);
    setAppShortcuts(data.appShortcuts);
    setSkills(data.skills);
  }, []);

  // Add a new message
  const addMessage = (content, sender) => {
    const newMessage = {
      id: Date.now().toString(),
      content,
      sender,
      timestamp: new Date(),
    };
    
    setMessages(prevMessages => [...prevMessages, newMessage]);
    
    // If it's a user message, simulate an assistant response
    if (sender === MessageType.USER) {
      setIsProcessing(true);
      
      // Simulate processing delay
      setTimeout(() => {
        const assistantResponse = {
          id: (Date.now() + 1).toString(),
          content: generateAssistantResponse(content),
          sender: MessageType.ASSISTANT,
          timestamp: new Date(),
        };
        
        setMessages(prevMessages => [...prevMessages, assistantResponse]);
        setIsProcessing(false);
      }, 1500);
    }
  };

  // Generate a simple response based on user input
  const generateAssistantResponse = (userMessage) => {
    const lowerCaseMessage = userMessage.toLowerCase();
    
    if (lowerCaseMessage.includes('hello') || lowerCaseMessage.includes('hi')) {
      return `Hello ${userProfile.name}. How can I assist you today?`;
    } else {
      return "I understand your request. Is there anything specific you'd like me to help you with?";
    }
  };

  const toggleTaskCompletion = (id) => {
    setTasks(prevTasks =>
      prevTasks.map(task =>
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };

  const toggleDeviceStatus = (id) => {
    setSmartDevices(prevDevices =>
      prevDevices.map(device =>
        device.id === id
          ? {
              ...device,
              status: device.status === 'on' ? 'off' : 'on',
            }
          : device
      )
    );
  };

  const launchApp = (id) => {
    // In a real app, this would launch the actual application
    console.log(`Launching app with id: ${id}`);
  };

  const toggleSkill = (id) => {
    setSkills(prevSkills =>
      prevSkills.map(skill =>
        skill.id === id ? { ...skill, enabled: !skill.enabled } : skill
      )
    );
  };

  const toggleListening = () => {
    setIsListening(prev => !prev);
  };

  const toggleOfflineMode = () => {
    setIsOfflineMode(prev => !prev);
  };

  const authenticate = (value) => {
    setIsAuthenticated(value);
  };

  // Context value
  const contextValue = {
    messages,
    tasks,
    smartDevices,
    appShortcuts,
    userProfile,
    skills,
    activeView,
    isListening,
    isProcessing,
    isAuthenticated,
    isOfflineMode,
    addMessage,
    toggleTaskCompletion,
    toggleDeviceStatus,
    launchApp,
    toggleSkill,
    setActiveView,
    toggleListening,
    toggleOfflineMode,
    authenticate,
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the context
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
