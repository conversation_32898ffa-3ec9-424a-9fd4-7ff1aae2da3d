from django.db import models
from users.models import User

class WifiHotspot(models.Model):
    name = models.CharField(max_length=100)
    location = models.CharField(max_length=200)
    status = models.CharField(max_length=20)
    coverage_radius = models.IntegerField()  # in meters
    coordinates = models.JSONField()
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'wifi_hotspots'

class HotspotUsage(models.Model):
    hotspot = models.ForeignKey(WifiHotspot, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    connected_at = models.DateTimeField()
    disconnected_at = models.DateTimeField(null=True)
    data_used = models.BigIntegerField(default=0)  # in bytes

    class Meta:
        db_table = 'hotspot_usage'