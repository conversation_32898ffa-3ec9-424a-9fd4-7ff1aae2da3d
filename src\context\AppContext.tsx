import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Message, Task, SmartDevice, AppShortcut, UserProfile, Skill } from '../types';
import { generateDummyData } from '../utils/dummyData';

interface AppContextType {
  messages: Message[];
  tasks: Task[];
  smartDevices: SmartDevice[];
  appShortcuts: AppShortcut[];
  userProfile: UserProfile;
  skills: Skill[];
  activeView: string;
  isListening: boolean;
  isProcessing: boolean;
  isAuthenticated: boolean;
  isOfflineMode: boolean;
  addMessage: (content: string, sender: 'user' | 'assistant') => void;
  toggleTaskCompletion: (id: string) => void;
  toggleDeviceStatus: (id: string) => void;
  launchApp: (id: string) => void;
  toggleSkill: (id: string) => void;
  setActiveView: (view: string) => void;
  toggleListening: () => void;
  toggleOfflineMode: () => void;
  authenticate: (value: boolean) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [smartDevices, setSmartDevices] = useState<SmartDevice[]>([]);
  const [appShortcuts, setAppShortcuts] = useState<AppShortcut[]>([]);
  const [userProfile, setUserProfile] = useState<UserProfile>({
    name: 'Tony',
    preferences: {
      theme: 'dark',
      voiceAssistant: true,
      notifications: true,
    },
  });
  const [skills, setSkills] = useState<Skill[]>([]);
  const [activeView, setActiveView] = useState('dashboard');
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(true);
  const [isOfflineMode, setIsOfflineMode] = useState(false);

  // Initialize with dummy data
  useEffect(() => {
    const data = generateDummyData();
    setMessages(data.messages);
    setTasks(data.tasks);
    setSmartDevices(data.smartDevices);
    setAppShortcuts(data.appShortcuts);
    setSkills(data.skills);
  }, []);

  const addMessage = (content: string, sender: 'user' | 'assistant') => {
    const newMessage: Message = {
      id: Date.now().toString(),
      content,
      sender,
      timestamp: new Date(),
    };
    
    setMessages((prev) => [...prev, newMessage]);
    
    // Simulate assistant response
    if (sender === 'user') {
      setIsProcessing(true);
      setTimeout(() => {
        const responseMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: getAssistantResponse(content),
          sender: 'assistant',
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, responseMessage]);
        setIsProcessing(false);
      }, 1500);
    }
  };

  const getAssistantResponse = (userMessage: string): string => {
    const lowerCaseMessage = userMessage.toLowerCase();
    
    if (lowerCaseMessage.includes('weather')) {
      return "The current weather is 72°F with clear skies. There's a 10% chance of rain later today.";
    } else if (lowerCaseMessage.includes('time')) {
      return `The current time is ${new Date().toLocaleTimeString()}.`;
    } else if (lowerCaseMessage.includes('task') || lowerCaseMessage.includes('reminder')) {
      return "I've added a new task to your list. Would you like to set a due date for it?";
    } else if (lowerCaseMessage.includes('smart') || lowerCaseMessage.includes('light') || lowerCaseMessage.includes('home')) {
      return "I've adjusted your smart home settings as requested.";
    } else if (lowerCaseMessage.includes('hello') || lowerCaseMessage.includes('hi')) {
      return `Hello ${userProfile.name}. How can I assist you today?`;
    } else {
      return "I understand your request. Is there anything specific you'd like me to help you with?";
    }
  };

  const toggleTaskCompletion = (id: string) => {
    setTasks((prev) =>
      prev.map((task) =>
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };

  const toggleDeviceStatus = (id: string) => {
    setSmartDevices((prev) =>
      prev.map((device) =>
        device.id === id
          ? {
              ...device,
              status: device.status === 'on' ? 'off' : 'on',
            }
          : device
      )
    );
  };

  const launchApp = (id: string) => {
    // In a real app, this would launch the actual application
    console.log(`Launching app with id: ${id}`);
  };

  const toggleSkill = (id: string) => {
    setSkills((prev) =>
      prev.map((skill) =>
        skill.id === id ? { ...skill, enabled: !skill.enabled } : skill
      )
    );
  };

  const toggleListening = () => {
    setIsListening((prev) => !prev);
    
    // Simulate stopping listening after 5 seconds
    if (!isListening) {
      setTimeout(() => {
        setIsListening(false);
        addMessage("What can I help you with today?", "user");
      }, 5000);
    }
  };

  const toggleOfflineMode = () => {
    setIsOfflineMode((prev) => !prev);
  };

  const authenticate = (value: boolean) => {
    setIsAuthenticated(value);
  };

  return (
    <AppContext.Provider
      value={{
        messages,
        tasks,
        smartDevices,
        appShortcuts,
        userProfile,
        skills,
        activeView,
        isListening,
        isProcessing,
        isAuthenticated,
        isOfflineMode,
        addMessage,
        toggleTaskCompletion,
        toggleDeviceStatus,
        launchApp,
        toggleSkill,
        setActiveView,
        toggleListening,
        toggleOfflineMode,
        authenticate,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};