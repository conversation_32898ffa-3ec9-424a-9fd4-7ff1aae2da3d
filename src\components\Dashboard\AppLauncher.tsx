import React from 'react';
import Card from '../UI/Card';
import { useAppContext } from '../../context/AppContext';
import * as LucideIcons from 'lucide-react';

const AppLauncher: React.FC = () => {
  const { appShortcuts, launchApp } = useAppContext();

  const getIconComponent = (iconName: string) => {
    // Convert kebab-case to PascalCase for Lucide icons
    const formattedIconName = iconName
      .split('-')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');

    // Check if the icon exists in LucideIcons
    if (formattedIconName in LucideIcons) {
      return LucideIcons[formattedIconName as keyof typeof LucideIcons];
    }

    // Fallback to App icon if the specified icon doesn't exist
    return LucideIcons.App;
  };

  return (
    <Card title="Quick Launch" className="h-full">
      <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-6 gap-3">
        {appShortcuts.map((app) => {
          const IconComponent = getIconComponent(app.icon);
          return (
            <button
              key={app.id}
              onClick={() => launchApp(app.id)}
              className="flex flex-col items-center justify-center p-3 rounded-lg border border-gray-800 bg-gray-800/30 hover:bg-gray-800 transition-colors"
            >
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-gray-700 to-gray-900 flex items-center justify-center mb-2">
                <IconComponent size={24} className="text-white" />
              </div>
              <span className="text-xs text-gray-300 text-center">{app.name}</span>
            </button>
          );
        })}
      </div>
    </Card>
  );
};

export default AppLauncher;