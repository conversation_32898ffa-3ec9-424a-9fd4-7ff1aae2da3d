import React, { useState, useEffect } from 'react';
import { StyleSheet, View, StatusBar } from 'react-native';
import { AppProvider } from './context/AppContext';
import MainLayout from './components/MainLayout';
import LoginScreen from './components/LoginScreen';
import SplashScreen from './components/SplashScreen';

export default function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <SplashScreen />;
  }

  return (
    <AppProvider>
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#0A0A18" />
        <MainLayout />
      </View>
    </AppProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0A0A18',
  },
});
