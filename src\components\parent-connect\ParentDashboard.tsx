import React from 'react';
import { Bell, Calendar, Award, Book, Coffee, Volume2 } from 'lucide-react';

const ParentDashboard: React.FC = () => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-soft overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-800">Parent Connect Dashboard</h3>
        <button 
          className="p-2 rounded-full bg-accent-100 text-accent-600 hover:bg-accent-200"
          aria-label="Play voice instructions"
        >
          <Volume2 size={18} />
        </button>
      </div>
      
      <div className="p-6">
        <div className="flex items-center justify-center mb-6">
          <div className="h-20 w-20 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 text-2xl font-bold">
            AR
          </div>
          <div className="ml-6">
            <h2 className="text-xl font-semibold text-gray-800">Aara<PERSON> <PERSON></h2>
            <p className="text-gray-600">Grade 5 • Teacher: Ms. <PERSON></p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="border border-gray-200 rounded-lg p-4 flex">
            <div className="h-12 w-12 rounded-full bg-success-100 flex items-center justify-center text-success-700 mr-4">
              <Calendar size={24} />
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700">Attendance</h4>
              <p className="text-xl font-semibold mt-1">90% <span className="text-sm font-normal text-gray-500">Present</span></p>
              <p className="text-xs text-gray-500">Last absent: 10 days ago</p>
            </div>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4 flex">
            <div className="h-12 w-12 rounded-full bg-accent-100 flex items-center justify-center text-accent-700 mr-4">
              <Award size={24} />
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700">Achievement</h4>
              <p className="text-xl font-semibold mt-1">15 <span className="text-sm font-normal text-gray-500">Badges</span></p>
              <p className="text-xs text-gray-500">3 challenges completed</p>
            </div>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4 flex">
            <div className="h-12 w-12 rounded-full bg-secondary-100 flex items-center justify-center text-secondary-700 mr-4">
              <Book size={24} />
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700">Learning Progress</h4>
              <p className="text-xl font-semibold mt-1">75% <span className="text-sm font-normal text-gray-500">Complete</span></p>
              <p className="text-xs text-gray-500">8 of 12 modules finished</p>
            </div>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4 flex">
            <div className="h-12 w-12 rounded-full bg-warning-100 flex items-center justify-center text-warning-700 mr-4">
              <Coffee size={24} />
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700">Meals Provided</h4>
              <p className="text-xl font-semibold mt-1">35 <span className="text-sm font-normal text-gray-500">Meals</span></p>
              <p className="text-xs text-gray-500">This month: 15 meals</p>
            </div>
          </div>
        </div>
        
        <div className="border border-gray-200 rounded-lg p-4 mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Upcoming Events</h4>
          
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 mr-3">
                <Calendar size={16} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-800">Parent-Teacher Meeting</p>
                <p className="text-xs text-gray-500">June 15, 2025 • 4:00 PM</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="h-8 w-8 rounded-full bg-accent-100 flex items-center justify-center text-accent-700 mr-3">
                <Award size={16} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-800">Science Fair</p>
                <p className="text-xs text-gray-500">June 20, 2025 • All Day</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Recent Notifications</h4>
          
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="h-8 w-8 rounded-full bg-error-100 flex items-center justify-center text-error-700 mr-3">
                <Bell size={16} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-800">Homework Due Tomorrow</p>
                <p className="text-xs text-gray-500">Mathematics - Page 42, Problems 1-10</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="h-8 w-8 rounded-full bg-success-100 flex items-center justify-center text-success-700 mr-3">
                <Bell size={16} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-800">Quiz Completed Successfully</p>
                <p className="text-xs text-gray-500">Science - Plants & Animals - Score: 85%</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="px-6 py-4 border-t border-gray-200 bg-primary-50 flex justify-between items-center">
        <button className="text-sm font-medium text-primary-600 hover:text-primary-700">
          Contact Teacher
        </button>
        <button className="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
          Schedule Meeting
        </button>
      </div>
    </div>
  );
};

export default ParentDashboard;