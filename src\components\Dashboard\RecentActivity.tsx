import React from 'react';
import { Clock, BookO<PERSON>, MessageCircle, Award } from 'lucide-react';

interface ActivityItem {
  id: string;
  title: string;
  type: 'course' | 'mentor' | 'challenge';
  time: string;
}

const RecentActivity: React.FC = () => {
  const activities: ActivityItem[] = [
    {
      id: '1',
      title: 'Completed "Basic Mathematics" module',
      type: 'course',
      time: '2 hours ago'
    },
    {
      id: '2',
      title: 'Asked AI Mentor about photosynthesis',
      type: 'mentor',
      time: '5 hours ago'
    },
    {
      id: '3',
      title: 'Started "Build a Simple Circuit" challenge',
      type: 'challenge',
      time: '1 day ago'
    },
    {
      id: '4',
      title: 'Completed "Reading Comprehension" quiz',
      type: 'course',
      time: '2 days ago'
    }
  ];

  const getIcon = (type: string) => {
    switch (type) {
      case 'course':
        return <BookOpen size={16} className="text-primary-500" />;
      case 'mentor':
        return <MessageCircle size={16} className="text-secondary-500" />;
      case 'challenge':
        return <Award size={16} className="text-accent-500" />;
      default:
        return <Clock size={16} className="text-gray-500" />;
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-soft overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 className="text-lg font-medium text-gray-800">Recent Activity</h3>
      </div>
      
      <div className="divide-y divide-gray-200">
        {activities.map((activity) => (
          <div key={activity.id} className="px-6 py-4 flex items-start">
            <div className="mr-3 mt-0.5">
              {getIcon(activity.type)}
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-800">{activity.title}</p>
              <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
            </div>
          </div>
        ))}
      </div>
      
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <button className="text-sm font-medium text-primary-600 hover:text-primary-700">
          View all activity
        </button>
      </div>
    </div>
  );
};

export default RecentActivity;