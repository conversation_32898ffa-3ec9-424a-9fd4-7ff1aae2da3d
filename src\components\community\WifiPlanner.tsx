import React, { useState } from 'react';
import { Wifi, MapPin, Home, School, Check, X } from 'lucide-react';

interface Hotspot {
  id: string;
  name: string;
  location: string;
  status: 'active' | 'inactive' | 'planned';
  coverage: number; // in meters
  connectedDevices: number;
  coordinates: {
    x: number;
    y: number;
  };
}

const WifiPlanner: React.FC = () => {
  const [hotspots, setHotspots] = useState<Hotspot[]>([
    {
      id: '1',
      name: 'Village Center',
      location: 'Community Hall',
      status: 'active',
      coverage: 200,
      connectedDevices: 15,
      coordinates: { x: 250, y: 150 }
    },
    {
      id: '2',
      name: 'School Campus',
      location: 'Main Building',
      status: 'active',
      coverage: 150,
      connectedDevices: 25,
      coordinates: { x: 100, y: 250 }
    },
    {
      id: '3',
      name: 'Health Center',
      location: 'Doctor\'s Office',
      status: 'planned',
      coverage: 100,
      connectedDevices: 0,
      coordinates: { x: 350, y: 200 }
    }
  ]);
  
  const [selectedHotspot, setSelectedHotspot] = useState<Hotspot | null>(null);
  const [newHotspotMode, setNewHotspotMode] = useState(false);
  const [mapPosition, setMapPosition] = useState({ x: 0, y: 0 });

  const handleHotspotClick = (hotspot: Hotspot) => {
    setSelectedHotspot(hotspot);
  };
  
  const handleMapClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!newHotspotMode) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const newHotspot: Hotspot = {
      id: Date.now().toString(),
      name: 'New Hotspot',
      location: 'Unspecified',
      status: 'planned',
      coverage: 100,
      connectedDevices: 0,
      coordinates: { x, y }
    };
    
    setHotspots([...hotspots, newHotspot]);
    setSelectedHotspot(newHotspot);
    setNewHotspotMode(false);
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-success-500';
      case 'inactive':
        return 'bg-error-500';
      case 'planned':
        return 'bg-warning-500';
      default:
        return 'bg-gray-500';
    }
  };
  
  const handleDragMap = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.buttons !== 1) return; // Only proceed if left mouse button is pressed
    
    setMapPosition({
      x: mapPosition.x + e.movementX,
      y: mapPosition.y + e.movementY
    });
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-soft overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-800">Community Wi-Fi Planning</h3>
        <div className="flex space-x-2">
          <button
            onClick={() => setNewHotspotMode(!newHotspotMode)}
            className={`px-3 py-1 rounded text-xs font-medium ${
              newHotspotMode 
                ? 'bg-primary-100 text-primary-700 border border-primary-300' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {newHotspotMode ? 'Cancel' : 'Add Hotspot'}
          </button>
        </div>
      </div>
      
      <div className="p-4 grid grid-cols-1 lg:grid-cols-3 gap-4">
        <div className="lg:col-span-2 border border-gray-200 rounded-lg overflow-hidden relative" style={{ height: '400px' }}>
          {/* Map UI */}
          <div 
            className="h-full w-full relative overflow-hidden bg-gray-100"
            onClick={handleMapClick}
            onMouseMove={handleDragMap}
            style={{ cursor: newHotspotMode ? 'crosshair' : 'grab' }}
          >
            {/* Village map representation */}
            <div 
              className="absolute inset-0 bg-gray-100"
              style={{ 
                transform: `translate(${mapPosition.x}px, ${mapPosition.y}px)`,
                backgroundImage: 'url(https://images.pexels.com/photos/1797121/pexels-photo-1797121.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                opacity: 0.3
              }}
            ></div>
            
            {/* Village buildings */}
            <div 
              className="absolute flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 text-primary-700"
              style={{ left: `${100 + mapPosition.x}px`, top: `${100 + mapPosition.y}px` }}
            >
              <School size={20} />
            </div>
            
            <div 
              className="absolute flex items-center justify-center h-12 w-12 rounded-full bg-accent-100 text-accent-700"
              style={{ left: `${250 + mapPosition.x}px`, top: `${150 + mapPosition.y}px` }}
            >
              <Home size={20} />
            </div>
            
            {/* Hotspots */}
            {hotspots.map((hotspot) => (
              <React.Fragment key={hotspot.id}>
                {/* Coverage radius */}
                <div 
                  className={`absolute rounded-full opacity-20 ${getStatusColor(hotspot.status)}`}
                  style={{ 
                    left: `${hotspot.coordinates.x + mapPosition.x - hotspot.coverage}px`, 
                    top: `${hotspot.coordinates.y + mapPosition.y - hotspot.coverage}px`,
                    width: `${hotspot.coverage * 2}px`,
                    height: `${hotspot.coverage * 2}px`
                  }}
                ></div>
                
                {/* Hotspot icon */}
                <div 
                  className={`absolute flex items-center justify-center h-10 w-10 rounded-full cursor-pointer ${
                    selectedHotspot?.id === hotspot.id
                      ? 'ring-4 ring-primary-300'
                      : ''
                  }`}
                  style={{ 
                    left: `${hotspot.coordinates.x + mapPosition.x - 20}px`, 
                    top: `${hotspot.coordinates.y + mapPosition.y - 20}px`,
                    backgroundColor: 'white',
                    color: hotspot.status === 'active' 
                      ? '#16a34a' 
                      : hotspot.status === 'inactive' 
                        ? '#dc2626' 
                        : '#eab308'
                  }}
                  onClick={() => handleHotspotClick(hotspot)}
                >
                  <Wifi size={18} />
                </div>
              </React.Fragment>
            ))}
            
            {newHotspotMode && (
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <p className="bg-white bg-opacity-75 px-4 py-2 rounded-full text-sm font-medium text-gray-800">
                  Click on the map to place a new hotspot
                </p>
              </div>
            )}
          </div>
          
          {/* Map controls */}
          <div className="absolute bottom-4 right-4 flex space-x-2">
            <button className="p-2 bg-white rounded-full shadow text-gray-700 hover:bg-gray-100">
              <span className="text-xl">+</span>
            </button>
            <button className="p-2 bg-white rounded-full shadow text-gray-700 hover:bg-gray-100">
              <span className="text-xl">-</span>
            </button>
          </div>
        </div>
        
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Hotspot Details</h4>
          
          {selectedHotspot ? (
            <div>
              <div className="mb-4">
                <label className="block text-xs font-medium text-gray-700 mb-1">Name</label>
                <input 
                  type="text" 
                  value={selectedHotspot.name}
                  onChange={(e) => setSelectedHotspot({ ...selectedHotspot, name: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded text-sm"
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-xs font-medium text-gray-700 mb-1">Location</label>
                <input 
                  type="text" 
                  value={selectedHotspot.location}
                  onChange={(e) => setSelectedHotspot({ ...selectedHotspot, location: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded text-sm"
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-xs font-medium text-gray-700 mb-1">Status</label>
                <select 
                  value={selectedHotspot.status}
                  onChange={(e) => setSelectedHotspot({ 
                    ...selectedHotspot, 
                    status: e.target.value as 'active' | 'inactive' | 'planned' 
                  })}
                  className="w-full p-2 border border-gray-300 rounded text-sm"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="planned">Planned</option>
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Coverage Radius (meters)
                </label>
                <input 
                  type="range" 
                  min="50" 
                  max="300" 
                  step="10"
                  value={selectedHotspot.coverage}
                  onChange={(e) => setSelectedHotspot({ 
                    ...selectedHotspot, 
                    coverage: parseInt(e.target.value) 
                  })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>50m</span>
                  <span>{selectedHotspot.coverage}m</span>
                  <span>300m</span>
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-xs font-medium text-gray-700 mb-1">Connected Devices</label>
                <div className="p-2 bg-gray-50 border border-gray-200 rounded text-sm">
                  {selectedHotspot.status === 'active' ? selectedHotspot.connectedDevices : 'Not active'}
                </div>
              </div>
              
              <div className="flex space-x-2 mt-6">
                <button
                  onClick={() => {
                    setHotspots(hotspots.map(h => 
                      h.id === selectedHotspot.id ? selectedHotspot : h
                    ));
                  }}
                  className="flex-1 px-3 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 text-sm font-medium"
                >
                  Save Changes
                </button>
                
                <button
                  onClick={() => {
                    setHotspots(hotspots.filter(h => h.id !== selectedHotspot.id));
                    setSelectedHotspot(null);
                  }}
                  className="px-3 py-2 bg-error-100 text-error-700 rounded hover:bg-error-200 text-sm font-medium"
                >
                  Delete
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-10">
              <MapPin size={32} className="mx-auto text-gray-300 mb-3" />
              <p className="text-sm text-gray-500">
                {newHotspotMode 
                  ? "Click on the map to add a hotspot" 
                  : "Select a hotspot to view details"}
              </p>
            </div>
          )}
          
          <div className="mt-6 border-t border-gray-200 pt-4">
            <h5 className="text-xs font-medium text-gray-700 mb-3">Coverage Summary</h5>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="h-3 w-3 rounded-full bg-success-500 mr-2"></div>
                  <span className="text-xs text-gray-700">Active</span>
                </div>
                <span className="text-xs font-medium">{hotspots.filter(h => h.status === 'active').length}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="h-3 w-3 rounded-full bg-error-500 mr-2"></div>
                  <span className="text-xs text-gray-700">Inactive</span>
                </div>
                <span className="text-xs font-medium">{hotspots.filter(h => h.status === 'inactive').length}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="h-3 w-3 rounded-full bg-warning-500 mr-2"></div>
                  <span className="text-xs text-gray-700">Planned</span>
                </div>
                <span className="text-xs font-medium">{hotspots.filter(h => h.status === 'planned').length}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WifiPlanner;