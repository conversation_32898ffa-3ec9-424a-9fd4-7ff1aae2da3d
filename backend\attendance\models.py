from django.db import models
from users.models import User

class AttendanceRecord(models.Model):
    student = models.ForeignKey(User, on_delete=models.CASCADE)
    date = models.DateField()
    status = models.CharField(max_length=20)
    meal_provided = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    recorded_by = models.ForeignKey(User, related_name='recorded_attendance', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'attendance_records'
        unique_together = ['student', 'date']

class MealDistribution(models.Model):
    student = models.ForeignKey(User, on_delete=models.CASCADE)
    date = models.DateField()
    meal_type = models.CharField(max_length=20)
    provided = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    distributed_by = models.ForeignKey(User, related_name='distributed_meals', on_delete=models.SET_NULL, null=True)

    class Meta:
        db_table = 'meal_distributions'
        unique_together = ['student', 'date', 'meal_type']