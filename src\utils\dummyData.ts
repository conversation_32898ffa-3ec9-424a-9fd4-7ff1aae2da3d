import { Message, Task, SmartDevice, AppShortcut, Skill } from '../types';

export const generateDummyData = () => {
  // Messages
  const messages: Message[] = [
    {
      id: '1',
      content: 'Hello BAT, what can you do for me?',
      sender: 'user',
      timestamp: new Date(Date.now() - 3600000),
    },
    {
      id: '2',
      content: "Hello <PERSON>. I'm BA<PERSON>, your personal AI assistant. I can help with tasks, control your smart home, access files, and assist with various information needs. How can I help you today?",
      sender: 'assistant',
      timestamp: new Date(Date.now() - 3590000),
    },
    {
      id: '3',
      content: 'What is on my schedule today?',
      sender: 'user',
      timestamp: new Date(Date.now() - 1800000),
    },
    {
      id: '4',
      content: "You have a meeting with the development team at 2:00 PM, a dentist appointment at 4:30 PM, and dinner reservations at Nobu for 7:00 PM. Would you like me to remind you before each event?",
      sender: 'assistant',
      timestamp: new Date(Date.now() - 1790000),
    },
  ];

  // Tasks
  const tasks: Task[] = [
    {
      id: '1',
      title: 'Complete project proposal',
      completed: false,
      priority: 'high',
      dueDate: new Date(Date.now() + 86400000), // Tomorrow
      category: 'work',
    },
    {
      id: '2',
      title: 'Buy groceries',
      completed: false,
      priority: 'medium',
      dueDate: new Date(Date.now() + 172800000), // Day after tomorrow
      category: 'personal',
    },
    {
      id: '3',
      title: 'Call mom',
      completed: true,
      priority: 'medium',
      category: 'personal',
    },
    {
      id: '4',
      title: 'Schedule team meeting',
      completed: false,
      priority: 'high',
      dueDate: new Date(Date.now() + 86400000), // Tomorrow
      category: 'work',
    },
    {
      id: '5',
      title: 'Review quarterly reports',
      completed: false,
      priority: 'high',
      dueDate: new Date(Date.now() + 259200000), // 3 days from now
      category: 'work',
    },
  ];

  // Smart Devices
  const smartDevices: SmartDevice[] = [
    {
      id: '1',
      name: 'Living Room Lights',
      type: 'light',
      status: 'on',
      location: 'Living Room',
    },
    {
      id: '2',
      name: 'Bedroom Lights',
      type: 'light',
      status: 'off',
      location: 'Bedroom',
    },
    {
      id: '3',
      name: 'Front Door Camera',
      type: 'camera',
      status: 'on',
      batteryLevel: 78,
      location: 'Front Door',
    },
    {
      id: '4',
      name: 'Kitchen Speaker',
      type: 'speaker',
      status: 'standby',
      location: 'Kitchen',
    },
    {
      id: '5',
      name: 'Living Room Thermostat',
      type: 'thermostat',
      status: 'on',
      location: 'Living Room',
    },
  ];

  // App Shortcuts
  const appShortcuts: AppShortcut[] = [
    {
      id: '1',
      name: 'WhatsApp',
      icon: 'message-circle',
      category: 'social',
    },
    {
      id: '2',
      name: 'Chrome',
      icon: 'globe',
      category: 'utility',
    },
    {
      id: '3',
      name: 'Spotify',
      icon: 'music',
      category: 'entertainment',
    },
    {
      id: '4',
      name: 'Gmail',
      icon: 'mail',
      category: 'productivity',
    },
    {
      id: '5',
      name: 'Calendar',
      icon: 'calendar',
      category: 'productivity',
    },
    {
      id: '6',
      name: 'Files',
      icon: 'folder',
      category: 'utility',
    },
  ];

  // Skills
  const skills: Skill[] = [
    {
      id: '1',
      name: 'Weather',
      description: 'Get real-time weather updates and forecasts',
      icon: 'cloud',
      enabled: true,
      category: 'information',
    },
    {
      id: '2',
      name: 'Calendar',
      description: 'Manage your schedule and appointments',
      icon: 'calendar',
      enabled: true,
      category: 'productivity',
    },
    {
      id: '3',
      name: 'Smart Home',
      description: 'Control your connected devices',
      icon: 'home',
      enabled: true,
      category: 'home',
    },
    {
      id: '4',
      name: 'News Reader',
      description: 'Get the latest news updates',
      icon: 'newspaper',
      enabled: false,
      category: 'information',
    },
    {
      id: '5',
      name: 'Email',
      description: 'Check and send emails',
      icon: 'mail',
      enabled: true,
      category: 'productivity',
    },
    {
      id: '6',
      name: 'Music Control',
      description: 'Control music playback',
      icon: 'music',
      enabled: false,
      category: 'entertainment',
    },
    {
      id: '7',
      name: 'File Browser',
      description: 'Access and manage your files',
      icon: 'folder',
      enabled: true,
      category: 'utility',
    },
    {
      id: '8',
      name: 'Currency Converter',
      description: 'Convert between different currencies',
      icon: 'dollar-sign',
      enabled: false,
      category: 'utility',
    },
  ];

  return {
    messages,
    tasks,
    smartDevices,
    appShortcuts,
    skills,
  };
};