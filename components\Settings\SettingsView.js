import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import Card from '../UI/Card';

const SettingsView = () => {
  return (
    <View style={styles.container}>
      <Card>
        <Text style={styles.text}>Settings View</Text>
        <Text style={styles.subtext}>This feature will be implemented soon.</Text>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#0A0A18',
  },
  text: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtext: {
    color: '#6E6E8E',
    fontSize: 14,
  },
});

export default SettingsView;
