import React from 'react';
import { Skill } from '../../types';
import * as LucideIcons from 'lucide-react';

interface SkillItemProps {
  skill: Skill;
  onToggle: (id: string) => void;
}

const SkillItem: React.FC<SkillItemProps> = ({ skill, onToggle }) => {
  const IconComponent = (LucideIcons as Record<string, React.FC<{ size?: number; className?: string }>>)[skill.icon] || LucideIcons.HelpCircle;
  
  return (
    <div className="p-3 rounded-lg border border-gray-800 bg-gray-800/30 flex items-center justify-between">
      <div className="flex items-center">
        <div className={`p-2 rounded-full mr-3 ${skill.enabled ? 'bg-cyan-900/50 text-cyan-400' : 'bg-gray-800 text-gray-500'}`}>
          <IconComponent size={20} />
        </div>
        <div>
          <p className="font-medium text-white">{skill.name}</p>
          <p className="text-xs text-gray-400">{skill.description}</p>
        </div>
      </div>
      
      <button
        onClick={() => onToggle(skill.id)}
        className={`
          relative inline-flex h-6 w-11 items-center rounded-full transition-colors
          ${skill.enabled ? 'bg-cyan-600' : 'bg-gray-700'}
        `}
      >
        <span
          className={`
            inline-block h-4 w-4 transform rounded-full bg-white transition-transform
            ${skill.enabled ? 'translate-x-6' : 'translate-x-1'}
          `}
        />
      </button>
    </div>
  );
};

export default SkillItem;