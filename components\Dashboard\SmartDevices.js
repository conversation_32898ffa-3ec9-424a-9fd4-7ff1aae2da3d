import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Switch } from 'react-native';
import { useAppContext } from '../../context/AppContext';
import { Feather } from '@expo/vector-icons';
import Card from '../UI/Card';
import { DeviceType, DeviceStatus } from '../../types';

const SmartDevices = () => {
  const { smartDevices, toggleDeviceStatus } = useAppContext();

  const getDeviceIcon = (type) => {
    switch (type) {
      case DeviceType.LIGHT:
        return <Feather name="sun" size={20} color="#FFFFFF" />;
      case DeviceType.THERMOSTAT:
        return <Feather name="thermometer" size={20} color="#FFFFFF" />;
      case DeviceType.CAMERA:
        return <Feather name="camera" size={20} color="#FFFFFF" />;
      case DeviceType.SPEAKER:
        return <Feather name="speaker" size={20} color="#FFFFFF" />;
      default:
        return <Feather name="box" size={20} color="#FFFFFF" />;
    }
  };

  return (
    <Card title="Smart Devices">
      <View style={styles.deviceList}>
        {smartDevices.map((device) => (
          <View key={device.id} style={styles.deviceItem}>
            <View style={styles.deviceInfo}>
              <View style={[
                styles.deviceIcon,
                device.status === DeviceStatus.ON ? styles.deviceIconActive : styles.deviceIconInactive
              ]}>
                {getDeviceIcon(device.type)}
              </View>
              <View style={styles.deviceDetails}>
                <Text style={styles.deviceName}>{device.name}</Text>
                <Text style={styles.deviceLocation}>{device.location}</Text>
              </View>
            </View>
            <Switch
              value={device.status === DeviceStatus.ON}
              onValueChange={() => toggleDeviceStatus(device.id)}
              trackColor={{ false: '#1D1D3B', true: '#0BC5C5' }}
              thumbColor="#FFFFFF"
            />
          </View>
        ))}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  deviceList: {
    marginTop: 8,
  },
  deviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#1D1D3B',
  },
  deviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deviceIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  deviceIconActive: {
    backgroundColor: '#0BC5C5',
  },
  deviceIconInactive: {
    backgroundColor: '#1D1D3B',
  },
  deviceDetails: {
    justifyContent: 'center',
  },
  deviceName: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  deviceLocation: {
    color: '#6E6E8E',
    fontSize: 12,
  },
});

export default SmartDevices;
