export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
}

export interface Task {
  id: string;
  title: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  dueDate?: Date;
  category?: string;
}

export interface SmartDevice {
  id: string;
  name: string;
  type: 'light' | 'thermostat' | 'camera' | 'speaker' | 'other';
  status: 'on' | 'off' | 'standby';
  batteryLevel?: number;
  location: string;
}

export interface AppShortcut {
  id: string;
  name: string;
  icon: string;
  category: 'productivity' | 'entertainment' | 'utility' | 'social' | 'other';
}

export interface UserProfile {
  name: string;
  avatar?: string;
  preferences: {
    theme: 'dark' | 'light' | 'system';
    voiceAssistant: boolean;
    notifications: boolean;
  };
}

export interface Skill {
  id: string;
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
  category: 'productivity' | 'home' | 'information' | 'utility' | 'custom';
}