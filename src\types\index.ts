// Common Types
export interface User {
  id: string;
  name: string;
  role: 'student' | 'teacher' | 'parent' | 'admin';
  avatarUrl?: string;
  grade?: string;
  school?: string;
  village?: string;
  lastSyncedAt?: string;
}

export interface Course {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  language: string;
  grade: string;
  subject: string;
  modules: Module[];
  downloadStatus: 'not-downloaded' | 'downloading' | 'downloaded';
  downloadSize: string;
}

export interface Module {
  id: string;
  title: string;
  type: 'video' | 'audio' | 'text' | 'quiz';
  duration: string;
  completionStatus: 'not-started' | 'in-progress' | 'completed';
  resources: Resource[];
}

export interface Resource {
  id: string;
  title: string;
  type: 'video' | 'audio' | 'pdf' | 'image' | 'text';
  url: string;
  offlineAvailable: boolean;
}

export interface AttendanceRecord {
  id: string;
  studentId: string;
  date: string;
  status: 'present' | 'absent' | 'late';
  mealProvided: boolean;
  notes?: string;
}

export interface Challenge {
  id: string;
  title: string;
  description: string;
  type: 'drawing' | 'craft' | 'coding' | 'writing';
  thumbnailUrl: string;
  deadline: string;
  reward: string;
  completionStatus: 'not-started' | 'in-progress' | 'submitted' | 'completed';
}

export interface NavigationItem {
  name: string;
  path: string;
  icon: string;
  requiresInternet?: boolean;
}

export interface AIMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
  type: 'text' | 'voice';
}

export interface SyncStatus {
  lastSynced: string | null;
  syncInProgress: boolean;
  pendingChanges: number;
}