import React from 'react';
import { RefreshCw } from 'lucide-react';

interface SyncIndicatorProps {
  pendingChanges: number;
}

const SyncIndicator: React.FC<SyncIndicatorProps> = ({ pendingChanges }) => {
  return (
    <div className="bg-secondary-50 border-b border-secondary-200 px-4 py-2">
      <div className="flex items-center">
        <RefreshCw size={16} className="text-secondary-500 mr-2 animate-spin" />
        <span className="text-sm font-medium text-secondary-800">
          Syncing {pendingChanges} {pendingChanges === 1 ? 'change' : 'changes'}...
        </span>
      </div>
    </div>
  );
};

export default SyncIndicator;