import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import Dashboard from './pages/Dashboard';
import Learning from './pages/Learning';
import AIMentor from './pages/AIMentor';
import ParentConnect from './pages/ParentConnect';
import Attendance from './pages/Attendance';
import Community from './pages/Community';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route path="learning" element={<Learning />} />
          <Route path="ai-mentor" element={<AIMentor />} />
          <Route path="parent-connect" element={<ParentConnect />} />
          <Route path="attendance" element={<Attendance />} />
          <Route path="community" element={<Community />} />
          <Route path="*" element={<Navigate to="/\" replace />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;