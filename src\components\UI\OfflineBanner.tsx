import React from 'react';
import { WifiOff, AlertCircle } from 'lucide-react';

interface OfflineBannerProps {
  pendingChanges: number;
}

const OfflineBanner: React.FC<OfflineBannerProps> = ({ pendingChanges }) => {
  return (
    <div className="bg-warning-50 border-b border-warning-200 px-4 py-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <WifiOff size={16} className="text-warning-500 mr-2" />
          <span className="text-sm font-medium text-warning-800">
            You're currently offline. Some features may be limited.
          </span>
        </div>
        
        {pendingChanges > 0 && (
          <div className="flex items-center text-xs font-medium text-warning-700">
            <AlertCircle size={14} className="mr-1" />
            <span>{pendingChanges} changes pending sync</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default OfflineBanner;