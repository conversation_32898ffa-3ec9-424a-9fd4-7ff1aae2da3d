import React from 'react';
import { BookOpen } from 'lucide-react';

interface LogoProps {
  compact?: boolean;
}

const Logo: React.FC<LogoProps> = ({ compact = false }) => {
  return (
    <div className="flex items-center">
      <div className="bg-primary-500 text-white p-1.5 rounded-md">
        <BookOpen size={compact ? 20 : 24} />
      </div>
      {!compact && (
        <div className="ml-2">
          <span className="font-display font-bold text-primary-700 text-lg">Vikalpa</span>
        </div>
      )}
    </div>
  );
};

export default Logo;