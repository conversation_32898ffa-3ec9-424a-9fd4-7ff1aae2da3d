import React from 'react';
import Card from '../UI/Card';
import Button from '../UI/Button';
import * as LucideIcons from 'lucide-react';

const FilesView: React.FC = () => {
  // Mock file data
  const recentFiles = [
    { id: '1', name: 'Project Proposal.docx', type: 'document', size: '256 KB', modified: '2 hours ago' },
    { id: '2', name: 'Budget 2025.xlsx', type: 'spreadsheet', size: '1.2 MB', modified: '1 day ago' },
    { id: '3', name: 'Presentation.pptx', type: 'presentation', size: '4.8 MB', modified: '3 days ago' },
    { id: '4', name: 'profile_pic.jpg', type: 'image', size: '2.3 MB', modified: '1 week ago' },
    { id: '5', name: 'meeting_notes.txt', type: 'text', size: '12 KB', modified: '2 weeks ago' },
  ];
  
  const folders = [
    { id: '1', name: 'Documents', count: 24 },
    { id: '2', name: 'Images', count: 156 },
    { id: '3', name: 'Videos', count: 8 },
    { id: '4', name: 'Downloads', count: 32 },
    { id: '5', name: 'Projects', count: 15 },
  ];
  
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <LucideIcons.FileText size={20} className="text-blue-400" />;
      case 'spreadsheet':
        return <LucideIcons.FileSpreadsheet size={20} className="text-green-400" />;
      case 'presentation':
        return <LucideIcons.FilePresentation size={20} className="text-orange-400" />;
      case 'image':
        return <LucideIcons.Image size={20} className="text-purple-400" />;
      case 'text':
        return <LucideIcons.FileText size={20} className="text-gray-400" />;
      default:
        return <LucideIcons.File size={20} className="text-gray-400" />;
    }
  };
  
  return (
    <div className="flex flex-col h-full overflow-auto p-4 space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        <Card title="Storage Overview" className="col-span-full xl:col-span-1">
          <div className="flex flex-col items-center">
            <div className="relative w-32 h-32 mb-4">
              <svg className="w-full h-full" viewBox="0 0 100 100">
                <circle
                  className="text-gray-800"
                  strokeWidth="10"
                  stroke="currentColor"
                  fill="transparent"
                  r="40"
                  cx="50"
                  cy="50"
                />
                <circle
                  className="text-cyan-500"
                  strokeWidth="10"
                  strokeDasharray="251.2"
                  strokeDashoffset="100.48"
                  strokeLinecap="round"
                  stroke="currentColor"
                  fill="transparent"
                  r="40"
                  cx="50"
                  cy="50"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <p className="text-2xl font-bold text-white">60%</p>
                  <p className="text-xs text-gray-400">Used</p>
                </div>
              </div>
            </div>
            
            <div className="text-center">
              <p className="text-gray-400 text-sm">
                <span className="text-white font-bold">600 GB</span> used of <span className="text-white font-bold">1 TB</span>
              </p>
            </div>
            
            <Button variant="ghost" size="sm" className="mt-4">
              <LucideIcons.HardDrive size={14} className="mr-1" />
              Manage Storage
            </Button>
          </div>
        </Card>
        
        <Card title="Folders" className="col-span-full md:col-span-1">
          <div className="space-y-2">
            {folders.map((folder) => (
              <div
                key={folder.id}
                className="flex items-center justify-between p-3 rounded-lg border border-gray-800 bg-gray-800/30 hover:bg-gray-800/50 cursor-pointer"
              >
                <div className="flex items-center">
                  <div className="p-2 rounded-md bg-gray-800 mr-3">
                    <LucideIcons.Folder size={20} className="text-cyan-400" />
                  </div>
                  <div>
                    <p className="font-medium text-white">{folder.name}</p>
                    <p className="text-xs text-gray-400">{folder.count} files</p>
                  </div>
                </div>
                <LucideIcons.ChevronRight size={18} className="text-gray-500" />
              </div>
            ))}
          </div>
        </Card>
        
        <Card title="Quick Actions" className="col-span-full md:col-span-1">
          <div className="grid grid-cols-2 gap-3">
            <Button variant="secondary" className="flex items-center justify-center p-3">
              <LucideIcons.Upload size={18} className="mr-2" />
              Upload
            </Button>
            <Button variant="secondary" className="flex items-center justify-center p-3">
              <LucideIcons.FolderPlus size={18} className="mr-2" />
              New Folder
            </Button>
            <Button variant="secondary" className="flex items-center justify-center p-3">
              <LucideIcons.Download size={18} className="mr-2" />
              Download
            </Button>
            <Button variant="secondary" className="flex items-center justify-center p-3">
              <LucideIcons.Trash2 size={18} className="mr-2" />
              Delete
            </Button>
          </div>
        </Card>
      </div>
      
      <Card title="Recent Files">
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-800">
                <th className="text-left py-3 px-4 text-gray-400 font-medium">Name</th>
                <th className="text-left py-3 px-4 text-gray-400 font-medium">Size</th>
                <th className="text-left py-3 px-4 text-gray-400 font-medium">Modified</th>
                <th className="text-right py-3 px-4 text-gray-400 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {recentFiles.map((file) => (
                <tr key={file.id} className="border-b border-gray-800/50 hover:bg-gray-800/20">
                  <td className="py-3 px-4">
                    <div className="flex items-center">
                      <div className="mr-3">{getFileIcon(file.type)}</div>
                      <span className="text-white font-medium">{file.name}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-gray-400">{file.size}</td>
                  <td className="py-3 px-4 text-gray-400">{file.modified}</td>
                  <td className="py-3 px-4 text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <LucideIcons.Eye size={14} />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <LucideIcons.Download size={14} />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <LucideIcons.MoreVertical size={14} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default FilesView;