import React, { useState } from 'react';
import { StyleSheet, Text, View, TextInput, TouchableOpacity, Image } from 'react-native';
import { useAppContext } from '../context/AppContext';

const LoginScreen = () => {
  const { authenticate } = useAppContext();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const handleLogin = () => {
    // In a real app, you would validate credentials
    // For demo purposes, we'll just authenticate
    authenticate(true);
  };

  return (
    <View style={styles.container}>
      <View style={styles.logoContainer}>
        <View style={styles.logoCircle}>
          <Text style={styles.logoText}>BAT</Text>
        </View>
        <Text style={styles.title}>Biometric Activated Terminal</Text>
      </View>

      <View style={styles.formContainer}>
        <TextInput
          style={styles.input}
          placeholder="Username"
          placeholderTextColor="#6E6E8E"
          value={username}
          onChangeText={setUsername}
        />
        <TextInput
          style={styles.input}
          placeholder="Password"
          placeholderTextColor="#6E6E8E"
          secureTextEntry
          value={password}
          onChangeText={setPassword}
        />
        <TouchableOpacity style={styles.button} onPress={handleLogin}>
          <Text style={styles.buttonText}>Login</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.biometricContainer}>
        <Text style={styles.biometricText}>Or use biometric authentication</Text>
        <TouchableOpacity style={styles.biometricButton} onPress={handleLogin}>
          <Text style={styles.biometricButtonText}>Scan Fingerprint</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0A0A18',
    padding: 20,
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#0BC5C5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: 40,
  },
  input: {
    backgroundColor: '#12122A',
    borderRadius: 8,
    padding: 15,
    marginBottom: 16,
    color: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#2A2A4C',
  },
  button: {
    backgroundColor: '#0BC5C5',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  biometricContainer: {
    alignItems: 'center',
  },
  biometricText: {
    color: '#9E9EBE',
    marginBottom: 16,
  },
  biometricButton: {
    borderWidth: 1,
    borderColor: '#0BC5C5',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    width: '80%',
  },
  biometricButtonText: {
    color: '#0BC5C5',
    fontWeight: 'bold',
  },
});

export default LoginScreen;
