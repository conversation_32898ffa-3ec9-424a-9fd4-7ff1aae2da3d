from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    ROLES = (
        ('student', 'Student'),
        ('teacher', 'Teacher'),
        ('parent', 'Parent'),
        ('admin', 'Admin'),
    )

    role = models.CharField(max_length=10, choices=ROLES)
    grade = models.CharField(max_length=10, blank=True)
    school = models.CharField(max_length=100, blank=True)
    village = models.CharField(max_length=100, blank=True)
    last_synced_at = models.DateTimeField(null=True, blank=True)
    is_offline_enabled = models.BooleanField(default=False)

    class Meta:
        db_table = 'users'

class OfflineSync(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    sync_type = models.Char<PERSON>ield(max_length=50)
    data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    synced_at = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, default='pending')

    class Meta:
        db_table = 'offline_syncs'