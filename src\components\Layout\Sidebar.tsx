import React from 'react';
import { useAppContext } from '../../context/AppContext';
import * as LucideIcons from 'lucide-react';

const Sidebar: React.FC = () => {
  const { activeView, setActiveView, userProfile, authenticate } = useAppContext();

  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: 'LayoutDashboard' },
    { id: 'chat', label: 'Conversation', icon: 'MessageCircle' },
    { id: 'tasks', label: 'Tasks', icon: 'CheckSquare' },
    { id: 'files', label: 'Files', icon: 'Folder' },
    { id: 'settings', label: 'Settings', icon: 'Settings' },
  ];

  const getIcon = (iconName: string) => {
    const IconComponent = (LucideIcons as any)[iconName] || LucideIcons.Home;
    return <IconComponent size={20} />;
  };

  return (
    <div className="w-64 bg-gray-900 border-r border-gray-800 flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-800">
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center shadow-glow-cyan mr-3">
            <LucideIcons.Zap size={20} className="text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-white">BAT</h1>
            <p className="text-xs text-gray-400">Terminal</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navItems.map((item) => (
            <li key={item.id}>
              <button
                onClick={() => setActiveView(item.id)}
                className={`
                  w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors
                  ${activeView === item.id
                    ? 'bg-cyan-900/50 text-cyan-400 border border-cyan-800/50'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800'
                  }
                `}
              >
                <span className="mr-3">{getIcon(item.icon)}</span>
                {item.label}
              </button>
            </li>
          ))}
        </ul>
      </nav>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-800">
        <div className="flex items-center mb-4">
          <div className="h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center mr-3">
            <span className="text-sm font-bold text-white">
              {userProfile.name.charAt(0)}
            </span>
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-white">{userProfile.name}</p>
            <p className="text-xs text-gray-400">System Admin</p>
          </div>
        </div>

        <button
          onClick={() => authenticate(false)}
          className="w-full flex items-center justify-center px-4 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors"
        >
          <LucideIcons.LogOut size={16} className="mr-2" />
          Sign Out
        </button>
      </div>
    </div>
  );
};

export default Sidebar;