import React, { useState } from 'react';
import { useAppContext } from '../../context/AppContext';
import NavItem from '../UI/NavItem';
import Button from '../UI/Button';
import * as LucideIcons from 'lucide-react';

const Sidebar: React.FC = () => {
  const { activeView, setActiveView, toggleListening, isListening } = useAppContext();
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: 'LayoutDashboard' },
    { id: 'chat', label: 'Conversation', icon: 'MessageCircle' },
    { id: 'tasks', label: 'Tasks', icon: 'CheckSquare' },
    { id: 'files', label: 'Files', icon: 'Folder' },
    { id: 'settings', label: 'Settings', icon: 'Settings' },
  ];
  
  return (
    <div 
      className={`
        bg-gray-900 border-r border-gray-800 h-full transition-all duration-300
        ${isCollapsed ? 'w-16' : 'w-64'}
      `}
    >
      <div className="h-full flex flex-col">
        <div className="p-4 border-b border-gray-800 flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center">
              <div className="h-8 w-8 mr-2 rounded-md bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center shadow-glow-cyan">
                <LucideIcons.Zap size={16} className="text-white" />
              </div>
              <h1 className="text-xl font-bold text-white">BAT</h1>
            </div>
          )}
          
          {isCollapsed && (
            <div className="mx-auto">
              <div className="h-8 w-8 rounded-md bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center shadow-glow-cyan">
                <LucideIcons.Zap size={16} className="text-white" />
              </div>
            </div>
          )}
          
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="text-gray-500 hover:text-white transition-colors"
          >
            {isCollapsed ? (
              <LucideIcons.PanelRight size={18} />
            ) : (
              <LucideIcons.PanelLeft size={18} />
            )}
          </button>
        </div>
        
        <div className="p-3">
          <Button
            variant={isListening ? 'danger' : 'primary'}
            onClick={toggleListening}
            className={`${isCollapsed ? 'w-10 h-10 p-0' : 'w-full'} flex items-center justify-center`}
          >
            {isListening ? (
              <>
                {!isCollapsed && <span className="mr-2">Stop</span>}
                <LucideIcons.MicOff size={18} />
              </>
            ) : (
              <>
                {!isCollapsed && <span className="mr-2">Listen</span>}
                <LucideIcons.Mic size={18} />
              </>
            )}
          </Button>
        </div>
        
        <nav className="flex-1 overflow-y-auto py-4">
          <ul className="space-y-1 px-3">
            {navItems.map((item) => (
              <li key={item.id}>
                {isCollapsed ? (
                  <button
                    onClick={() => setActiveView(item.id)}
                    className={`
                      w-full flex items-center justify-center p-3 rounded-md transition-colors
                      ${activeView === item.id
                        ? 'bg-gray-800 text-cyan-400'
                        : 'text-gray-500 hover:text-gray-300 hover:bg-gray-800/50'}
                    `}
                  >
                    {React.createElement(
                      (LucideIcons as Record<string, React.FC<{ size?: number }>>)[item.icon] || LucideIcons.HelpCircle,
                      { size: 20 }
                    )}
                  </button>
                ) : (
                  <NavItem
                    icon={item.icon}
                    label={item.label}
                    active={activeView === item.id}
                    onClick={() => setActiveView(item.id)}
                  />
                )}
              </li>
            ))}
          </ul>
        </nav>
        
        <div className="p-4 border-t border-gray-800">
          {!isCollapsed && (
            <div className="flex items-center mb-4">
              <div className="h-8 w-8 mr-2 rounded-full bg-gray-700 flex items-center justify-center">
                <span className="text-sm font-bold text-white">T</span>
              </div>
              <div>
                <p className="text-sm font-medium text-white">Tony</p>
                <p className="text-xs text-gray-500">System Admin</p>
              </div>
            </div>
          )}
          
          <Button
            variant="ghost"
            className={`${isCollapsed ? 'w-10 h-10 p-0' : 'w-full'} flex items-center justify-center`}
          >
            {!isCollapsed && <span className="mr-2">Log out</span>}
            <LucideIcons.LogOut size={18} />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;