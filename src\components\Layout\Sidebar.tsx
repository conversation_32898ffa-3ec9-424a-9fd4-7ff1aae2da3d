import React from 'react';
import { NavLink } from 'react-router-dom';
import { Home, BookOpen, MessageCircle, Users, Calendar, Award, BookMarked, Wifi, User, Settings, WifiOff } from 'lucide-react';
import { NavigationItem } from '../../types';
import Logo from '../ui/Logo';

interface SidebarProps {
  isOpen: boolean;
  isOnline: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, isOnline }) => {
  const navigationItems: NavigationItem[] = [
    { name: 'Dashboard', path: '/', icon: 'Home' },
    { name: 'Learning', path: '/learning', icon: 'BookOpen' },
    { name: 'AI Mentor', path: '/ai-mentor', icon: 'MessageCircle', requiresInternet: true },
    { name: 'Parent Connect', path: '/parent-connect', icon: 'Users' },
    { name: 'Attendance', path: '/attendance', icon: 'Calendar' },
    { name: 'Challenges', path: '/challenges', icon: 'Award' },
    { name: 'Resources', path: '/resources', icon: 'BookMarked' },
    { name: 'Community Wi-Fi', path: '/community', icon: 'Wifi', requiresInternet: true },
    { name: 'Profile', path: '/profile', icon: 'User' },
    { name: 'Settings', path: '/settings', icon: 'Settings' },
  ];

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'Home': return <Home size={20} />;
      case 'BookOpen': return <BookOpen size={20} />;
      case 'MessageCircle': return <MessageCircle size={20} />;
      case 'Users': return <Users size={20} />;
      case 'Calendar': return <Calendar size={20} />;
      case 'Award': return <Award size={20} />;
      case 'BookMarked': return <BookMarked size={20} />;
      case 'Wifi': return <Wifi size={20} />;
      case 'User': return <User size={20} />;
      case 'Settings': return <Settings size={20} />;
      default: return <Home size={20} />;
    }
  };

  return (
    <aside className={`fixed inset-y-0 left-0 z-20 flex flex-col bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 lg:static lg:w-64`}>
      <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
        <Logo />
      </div>
      
      <div className="overflow-y-auto flex-1">
        <nav className="px-2 py-4">
          <ul className="space-y-1">
            {navigationItems.map((item) => {
              const isDisabled = item.requiresInternet && !isOnline;
              
              return (
                <li key={item.path}>
                  <NavLink
                    to={isDisabled ? '#' : item.path}
                    className={({ isActive }) => 
                      `flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors ${
                        isActive 
                          ? 'bg-primary-50 text-primary-700' 
                          : 'text-gray-700 hover:bg-gray-50 hover:text-primary-600'
                      } ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`
                    }
                    onClick={(e) => isDisabled && e.preventDefault()}
                  >
                    <span className="mr-3 flex-shrink-0">{getIcon(item.icon)}</span>
                    <span>{item.name}</span>
                    {isDisabled && <WifiOff size={16} className="ml-auto text-warning-500" />}
                  </NavLink>
                </li>
              );
            })}
          </ul>
        </nav>
      </div>
      
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3 bg-primary-50 p-3 rounded-lg">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 rounded-full bg-primary-200 flex items-center justify-center text-primary-700">
              <span className="text-sm font-bold">US</span>
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">User Student</p>
            <p className="text-xs text-gray-500 truncate">Grade 8</p>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;