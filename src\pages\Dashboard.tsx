import React from 'react';
import { BookO<PERSON>, Users, Award, Wifi } from 'lucide-react';
import StatCard from '../components/dashboard/StatCard';
import RecentActivity from '../components/dashboard/RecentActivity';
import DownloadableCourses from '../components/dashboard/DownloadableCourses';
import UpcomingChallenges from '../components/dashboard/UpcomingChallenges';

const Dashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Welcome back, Student!</h1>
        <p className="text-gray-600 mt-1">Here's what's happening with your learning journey</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
        <StatCard 
          title="Learning Modules" 
          value="8/12" 
          icon={<BookOpen size={24} />} 
          change={{ value: "3 this week", positive: true }}
          color="primary"
        />
        
        <StatCard 
          title="Attendance Rate" 
          value="90%" 
          icon={<Users size={24} />} 
          color="secondary"
        />
        
        <StatCard 
          title="Challenges Completed" 
          value="5" 
          icon={<Award size={24} />} 
          change={{ value: "2 new", positive: true }}
          color="accent"
        />
        
        <StatCard 
          title="Wi-Fi Availability" 
          value="Limited" 
          icon={<Wifi size={24} />} 
          color="warning"
        />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentActivity />
        <DownloadableCourses />
      </div>
      
      <div>
        <UpcomingChallenges />
      </div>
    </div>
  );
};

export default Dashboard;