import React, { useState } from 'react';
import { Search, Filter, BookOpen } from 'lucide-react';
import CourseCard from '../components/learning/CourseCard';
import { Course } from '../types';

const Learning: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  
  const [courses, setCourses] = useState<Course[]>([
    {
      id: '1',
      title: 'Mathematics Grade 5',
      description: 'Learn basic arithmetic, fractions, and decimals',
      thumbnailUrl: 'https://images.pexels.com/photos/3059654/pexels-photo-3059654.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      language: 'English',
      grade: '5',
      subject: 'Mathematics',
      modules: [],
      downloadStatus: 'not-downloaded',
      downloadSize: '25 MB'
    },
    {
      id: '2',
      title: 'Science Grade 5',
      description: 'Explore plants, animals, and simple machines',
      thumbnailUrl: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      language: 'English',
      grade: '5',
      subject: 'Science',
      modules: [],
      downloadStatus: 'not-downloaded',
      downloadSize: '40 MB'
    },
    {
      id: '3',
      title: 'English Grade 5',
      description: 'Improve reading, writing, and comprehension',
      thumbnailUrl: 'https://images.pexels.com/photos/256417/pexels-photo-256417.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      language: 'English',
      grade: '5',
      subject: 'English',
      modules: [],
      downloadStatus: 'downloaded',
      downloadSize: '15 MB'
    },
    {
      id: '4',
      title: 'Social Studies Grade 5',
      description: 'Learn about history, geography, and cultures',
      thumbnailUrl: 'https://images.pexels.com/photos/2393789/pexels-photo-2393789.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      language: 'English',
      grade: '5',
      subject: 'Social Studies',
      modules: [],
      downloadStatus: 'not-downloaded',
      downloadSize: '30 MB'
    },
    {
      id: '5',
      title: 'Art & Craft Grade 5',
      description: 'Express creativity through drawing and crafting',
      thumbnailUrl: 'https://images.pexels.com/photos/159579/crayons-coloring-book-coloring-book-159579.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      language: 'English',
      grade: '5',
      subject: 'Art',
      modules: [],
      downloadStatus: 'not-downloaded',
      downloadSize: '20 MB'
    },
    {
      id: '6',
      title: 'Physical Education Grade 5',
      description: 'Stay active with sports and exercises',
      thumbnailUrl: 'https://images.pexels.com/photos/8158152/pexels-photo-8158152.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      language: 'English',
      grade: '5',
      subject: 'Physical Education',
      modules: [],
      downloadStatus: 'not-downloaded',
      downloadSize: '18 MB'
    }
  ]);
  
  const handleDownload = (courseId: string) => {
    setCourses(courses.map(course => 
      course.id === courseId 
        ? { ...course, downloadStatus: 'downloading' }
        : course
    ));
    
    // Simulate download completion after 3 seconds
    setTimeout(() => {
      setCourses(courses.map(course => 
        course.id === courseId 
          ? { ...course, downloadStatus: 'downloaded' }
          : course
      ));
    }, 3000);
  };
  
  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.subject.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (activeFilter === 'all') return matchesSearch;
    if (activeFilter === 'downloaded') return matchesSearch && course.downloadStatus === 'downloaded';
    if (activeFilter === 'not-downloaded') return matchesSearch && course.downloadStatus === 'not-downloaded';
    
    return matchesSearch && course.subject.toLowerCase() === activeFilter.toLowerCase();
  });
  
  const subjects = Array.from(new Set(courses.map(course => course.subject)));

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Learning Modules</h1>
        <p className="text-gray-600 mt-1">Explore educational content for Grade 5</p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search for courses..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
        </div>
        
        <div className="flex items-center space-x-2 overflow-x-auto pb-2">
          <div className="flex items-center text-sm font-medium text-gray-500">
            <Filter size={16} className="mr-1" />
            <span>Filter:</span>
          </div>
          
          <button
            onClick={() => setActiveFilter('all')}
            className={`px-3 py-1 text-sm rounded-full whitespace-nowrap ${
              activeFilter === 'all'
                ? 'bg-primary-100 text-primary-700 border border-primary-200'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          
          <button
            onClick={() => setActiveFilter('downloaded')}
            className={`px-3 py-1 text-sm rounded-full whitespace-nowrap ${
              activeFilter === 'downloaded'
                ? 'bg-success-100 text-success-700 border border-success-200'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Downloaded
          </button>
          
          {subjects.map(subject => (
            <button
              key={subject}
              onClick={() => setActiveFilter(subject.toLowerCase())}
              className={`px-3 py-1 text-sm rounded-full whitespace-nowrap ${
                activeFilter === subject.toLowerCase()
                  ? 'bg-secondary-100 text-secondary-700 border border-secondary-200'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {subject}
            </button>
          ))}
        </div>
      </div>
      
      {filteredCourses.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCourses.map(course => (
            <CourseCard key={course.id} course={course} onDownload={handleDownload} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <BookOpen size={48} className="mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No courses found</h3>
          <p className="text-gray-500">
            Try adjusting your search or filter criteria to find courses.
          </p>
        </div>
      )}
    </div>
  );
};

export default Learning;