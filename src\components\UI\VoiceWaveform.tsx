import React, { useEffect, useRef } from 'react';

interface VoiceWaveformProps {
  isActive: boolean;
  color?: string;
}

const VoiceWaveform: React.FC<VoiceWaveformProps> = ({ isActive, color = '#0EF7F7' }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    let animationFrameId: number;
    const bars = 60;
    const barWidth = canvas.width / bars;
    
    const render = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      if (!isActive) {
        // Draw a flat line when not active
        ctx.beginPath();
        ctx.moveTo(0, canvas.height / 2);
        ctx.lineTo(canvas.width, canvas.height / 2);
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.stroke();
        animationFrameId = requestAnimationFrame(render);
        return;
      }
      
      // Draw animated waveform when active
      for (let i = 0; i < bars; i++) {
        const x = i * barWidth;
        const height = isActive 
          ? Math.random() * canvas.height * 0.6 + canvas.height * 0.2
          : canvas.height / 2;
        
        const y = (canvas.height - height) / 2;
        
        ctx.fillStyle = color;
        ctx.fillRect(x, y, barWidth - 1, height);
      }
      
      animationFrameId = requestAnimationFrame(render);
    };
    
    render();
    
    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [isActive, color]);
  
  return (
    <canvas 
      ref={canvasRef} 
      width={300} 
      height={60} 
      className="w-full h-full"
    />
  );
};

export default VoiceWaveform;