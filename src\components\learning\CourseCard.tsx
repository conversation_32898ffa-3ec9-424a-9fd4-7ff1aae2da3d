import React from 'react';
import { Download, Check, ArrowRight, Volume2 } from 'lucide-react';
import { Course } from '../../types';

interface CourseCardProps {
  course: Course;
  onDownload: (courseId: string) => void;
  onPlay?: () => void;
}

const CourseCard: React.FC<CourseCardProps> = ({ course, onDownload, onPlay }) => {
  const getSubjectColor = (subject: string) => {
    switch (subject.toLowerCase()) {
      case 'mathematics':
        return 'bg-math-500 hover:bg-math-600';
      case 'science':
        return 'bg-science-500 hover:bg-science-600';
      default:
        return 'bg-fun-500 hover:bg-fun-600';
    }
  };

  const getDownloadButton = () => {
    switch (course.downloadStatus) {
      case 'not-downloaded':
        return (
          <button 
            onClick={() => onDownload(course.id)}
            className="absolute top-3 right-3 p-3 bg-white rounded-full text-fun-500 hover:text-fun-600 shadow-fun transition-transform hover:scale-110"
            aria-label="Download course"
          >
            <Download size={24} />
          </button>
        );
      case 'downloading':
        return (
          <div className="absolute top-3 right-3 p-3 bg-white rounded-full text-candy-500 shadow-fun">
            <div className="h-6 w-6 rounded-full border-4 border-candy-500 border-t-transparent animate-spin"></div>
          </div>
        );
      case 'downloaded':
        return (
          <div className="absolute top-3 right-3 p-3 bg-white rounded-full text-science-500 shadow-fun">
            <Check size={24} />
          </div>
        );
    }
  };

  return (
    <div className="bg-white rounded-3xl border-4 border-fun-200 shadow-fun-lg overflow-hidden transform transition-all hover:-translate-y-2 hover:shadow-2xl">
      <div className="relative h-48">
        <img 
          src={course.thumbnailUrl} 
          alt={course.title} 
          className="h-full w-full object-cover"
        />
        
        {getDownloadButton()}
        
        {onPlay && (
          <button
            onClick={onPlay}
            className="absolute bottom-3 left-3 p-3 bg-white rounded-full text-fun-500 hover:text-fun-600 shadow-fun transition-transform hover:scale-110"
            aria-label="Play course introduction"
          >
            <Volume2 size={24} />
          </button>
        )}
        
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
          <div className="flex space-x-2">
            <span className={`px-4 py-2 rounded-full text-sm font-comic text-white ${getSubjectColor(course.subject)}`}>
              {course.subject}
            </span>
            <span className="px-4 py-2 rounded-full text-sm font-comic bg-candy-500 text-white">
              Grade {course.grade}
            </span>
          </div>
        </div>
      </div>
      
      <div className="p-6">
        <h3 className="text-xl font-comic text-gray-900 mb-2">{course.title}</h3>
        <p className="text-base font-comic text-gray-600 mb-4">{course.description}</p>
        
        <div className="flex justify-between items-center">
          <span className="text-sm font-comic text-gray-500">{course.downloadSize}</span>
          <button className={`px-6 py-3 rounded-full font-comic text-white shadow-fun transition-transform hover:scale-105 ${getSubjectColor(course.subject)}`}>
            Start Learning!
          </button>
        </div>
      </div>
    </div>
  );
};

export default CourseCard;