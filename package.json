{"name": "bat-expo", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~50.0.8", "expo-constants": "~15.4.5", "expo-linking": "~6.2.2", "expo-router": "~3.4.8", "expo-status-bar": "~1.11.1", "lucide-react-native": "^0.344.0", "react": "18.2.0", "react-native": "0.73.4", "react-native-gesture-handler": "~2.14.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "babel-preset-expo": "^13.1.11", "typescript": "^5.1.3"}, "private": true}