import React, { ReactNode } from 'react';

interface CardProps {
  children: ReactNode;
  title?: string;
  className?: string;
  glowing?: boolean;
}

const Card: React.FC<CardProps> = ({ children, title, className = '', glowing = false }) => {
  return (
    <div 
      className={`
        bg-gray-900/80 backdrop-blur-sm border border-gray-800 
        rounded-lg overflow-hidden ${glowing ? 'shadow-glow-cyan' : 'shadow-md'} 
        ${className}
      `}
    >
      {title && (
        <div className="border-b border-gray-800 px-4 py-3">
          <h3 className="text-cyan-400 font-semibold">{title}</h3>
        </div>
      )}
      <div className="p-4">{children}</div>
    </div>
  );
};

export default Card;