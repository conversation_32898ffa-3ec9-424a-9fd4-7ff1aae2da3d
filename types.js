// Define the types for our app
export const MessageType = {
  USER: 'user',
  ASSISTANT: 'assistant',
};

export const TaskPriority = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
};

export const DeviceType = {
  LIGHT: 'light',
  THERMOSTAT: 'thermostat',
  CAMERA: 'camera',
  SPEAKER: 'speaker',
  OTHER: 'other',
};

export const DeviceStatus = {
  ON: 'on',
  OFF: 'off',
  STANDBY: 'standby',
};

export const AppCategory = {
  PRODUCTIVITY: 'productivity',
  ENTERTAINMENT: 'entertainment',
  UTILITY: 'utility',
  SOCIAL: 'social',
  OTHER: 'other',
};

export const SkillCategory = {
  PRODUCTIVITY: 'productivity',
  ENTERTAINMENT: 'entertainment',
  UTILITY: 'utility',
  SOCIAL: 'social',
  OTHER: 'other',
};

export const ThemeType = {
  DARK: 'dark',
  LIGHT: 'light',
  SYSTEM: 'system',
};

export const ViewType = {
  DASHBOARD: 'dashboard',
  CHAT: 'chat',
  TASKS: 'tasks',
  FILES: 'files',
  SETTINGS: 'settings',
};
