import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import { useAppContext } from '../../context/AppContext';
import { Feather } from '@expo/vector-icons';
import Card from '../UI/Card';

const DashboardView = () => {
  const { userProfile, tasks } = useAppContext();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [refreshing, setRefreshing] = useState(false);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Get current time of day
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  // Get next upcoming task
  const getNextTask = () => {
    if (!tasks || tasks.length === 0) return null;

    const incompleteTasks = tasks.filter(task => !task.completed && task.dueDate);
    if (incompleteTasks.length === 0) return null;

    return incompleteTasks.sort((a, b) => {
      const dateA = a.dueDate ? new Date(a.dueDate).getTime() : Infinity;
      const dateB = b.dueDate ? new Date(b.dueDate).getTime() : Infinity;
      return dateA - dateB;
    })[0];
  };

  const nextTask = getNextTask();

  // Format time
  const formatTime = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const onRefresh = () => {
    setRefreshing(true);
    setCurrentTime(new Date());
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#0EF7F7" />
      }
    >
      <View style={styles.content}>
        <View style={styles.headerCards}>
          <Card style={styles.greetingCard}>
            <View style={styles.greetingContent}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>{userProfile?.name?.charAt(0) || 'U'}</Text>
              </View>
              <View>
                <Text style={styles.greeting}>
                  {getGreeting()}, {userProfile?.name || 'User'}
                </Text>
                <Text style={styles.date}>
                  {currentTime.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </Text>
              </View>
            </View>
          </Card>

          <Card style={styles.timeCard}>
            <View style={styles.timeContent}>
              <View style={styles.timeRow}>
                <Feather name="clock" size={24} color="#0EF7F7" style={styles.timeIcon} />
                <Text style={styles.time}>
                  {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
              </View>
              {nextTask && (
                <Text style={styles.nextTask}>
                  <Text style={styles.nextTaskLabel}>Next:</Text> {nextTask.title} at {formatTime(nextTask.dueDate)}
                </Text>
              )}
            </View>
          </Card>
        </View>

        <StatusWidgetsSimple />

        <View style={styles.widgetsRow}>
          <SmartDevicesSimple />
          <AppLauncherSimple />
        </View>
      </View>
    </ScrollView>
  );
};

// Simple StatusWidgets component
const StatusWidgetsSimple = () => {
  const { tasks, smartDevices, isOfflineMode, isListening } = useAppContext();

  const activeTasks = tasks ? tasks.filter((task) => !task.completed).length : 0;
  const activeDevices = smartDevices ? smartDevices.filter((device) => device.status === 'on').length : 0;

  return (
    <View style={statusStyles.container}>
      <View style={statusStyles.row}>
        <Card style={statusStyles.widget}>
          <View style={statusStyles.widgetContent}>
            <Feather name="check-square" size={24} color="#0EF7F7" />
            <Text style={statusStyles.value}>{activeTasks}</Text>
            <Text style={statusStyles.label}>Active Tasks</Text>
          </View>
        </Card>

        <Card style={statusStyles.widget}>
          <View style={statusStyles.widgetContent}>
            <Feather name="zap" size={24} color="#0EF7F7" />
            <Text style={statusStyles.value}>{activeDevices}</Text>
            <Text style={statusStyles.label}>Devices Online</Text>
          </View>
        </Card>
      </View>

      <View style={statusStyles.row}>
        <Card style={statusStyles.widget}>
          <View style={statusStyles.widgetContent}>
            <Feather
              name="wifi"
              size={24}
              color={isOfflineMode ? '#FF4D4D' : '#0EF7F7'}
            />
            <Text style={statusStyles.value}>
              {isOfflineMode ? 'Offline' : 'Online'}
            </Text>
            <Text style={statusStyles.label}>Network Status</Text>
          </View>
        </Card>

        <Card style={statusStyles.widget}>
          <View style={statusStyles.widgetContent}>
            <Feather
              name="mic"
              size={24}
              color={isListening ? '#4DFF88' : '#0EF7F7'}
            />
            <Text style={statusStyles.value}>
              {isListening ? 'Listening' : 'Ready'}
            </Text>
            <Text style={statusStyles.label}>Voice Status</Text>
          </View>
        </Card>
      </View>
    </View>
  );
};

// Simple SmartDevices component
const SmartDevicesSimple = () => {
  const { smartDevices, toggleDeviceStatus } = useAppContext();

  if (!smartDevices || smartDevices.length === 0) {
    return (
      <Card title="Smart Devices">
        <Text style={smartStyles.emptyText}>No smart devices available</Text>
      </Card>
    );
  }

  return (
    <Card title="Smart Devices">
      <View style={smartStyles.deviceList}>
        {smartDevices.map((device) => (
          <TouchableOpacity
            key={device.id}
            style={smartStyles.deviceItem}
            onPress={() => toggleDeviceStatus(device.id)}
          >
            <View style={smartStyles.deviceInfo}>
              <View style={[
                smartStyles.deviceIcon,
                device.status === 'on' ? smartStyles.deviceIconActive : smartStyles.deviceIconInactive
              ]}>
                <Feather
                  name={device.type === 'light' ? 'sun' :
                        device.type === 'thermostat' ? 'thermometer' :
                        device.type === 'camera' ? 'camera' :
                        device.type === 'speaker' ? 'speaker' : 'box'}
                  size={20}
                  color="#FFFFFF"
                />
              </View>
              <View style={smartStyles.deviceDetails}>
                <Text style={smartStyles.deviceName}>{device.name}</Text>
                <Text style={smartStyles.deviceLocation}>{device.location}</Text>
              </View>
            </View>
            <Text style={[
              smartStyles.deviceStatus,
              device.status === 'on' ? smartStyles.deviceStatusOn : smartStyles.deviceStatusOff
            ]}>
              {device.status === 'on' ? 'ON' : 'OFF'}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );
};

// Simple AppLauncher component
const AppLauncherSimple = () => {
  const { appShortcuts, launchApp } = useAppContext();

  if (!appShortcuts || appShortcuts.length === 0) {
    return (
      <Card title="Quick Launch">
        <Text style={appStyles.emptyText}>No apps available</Text>
      </Card>
    );
  }

  return (
    <Card title="Quick Launch">
      <View style={appStyles.appGrid}>
        {appShortcuts.map((app) => (
          <TouchableOpacity
            key={app.id}
            style={appStyles.appItem}
            onPress={() => launchApp(app.id)}
          >
            <View style={appStyles.appIcon}>
              <Feather name={app.icon.replace(/-([a-z])/g, (g) => g[1])} size={24} color="#FFFFFF" />
            </View>
            <Text style={appStyles.appName}>{app.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );
};

const statusStyles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  widget: {
    flex: 1,
    marginHorizontal: 4,
  },
  widgetContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  value: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  label: {
    color: '#6E6E8E',
    fontSize: 12,
  },
});

const smartStyles = StyleSheet.create({
  deviceList: {
    marginTop: 8,
  },
  deviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#1D1D3B',
  },
  deviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deviceIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  deviceIconActive: {
    backgroundColor: '#0BC5C5',
  },
  deviceIconInactive: {
    backgroundColor: '#1D1D3B',
  },
  deviceDetails: {
    justifyContent: 'center',
  },
  deviceName: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  deviceLocation: {
    color: '#6E6E8E',
    fontSize: 12,
  },
  deviceStatus: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  deviceStatusOn: {
    color: '#0BC5C5',
  },
  deviceStatusOff: {
    color: '#6E6E8E',
  },
  emptyText: {
    color: '#6E6E8E',
    textAlign: 'center',
    padding: 16,
  },
});

const appStyles = StyleSheet.create({
  appGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  appItem: {
    width: '30%',
    alignItems: 'center',
    marginBottom: 16,
  },
  appIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#1D1D3B',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  appName: {
    color: '#FFFFFF',
    fontSize: 12,
    textAlign: 'center',
  },
  emptyText: {
    color: '#6E6E8E',
    textAlign: 'center',
    padding: 16,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0A0A18',
  },
  content: {
    padding: 16,
  },
  headerCards: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  greetingCard: {
    flex: 2,
    marginRight: 8,
  },
  greetingContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#0BC5C5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  greeting: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  date: {
    color: '#6E6E8E',
    fontSize: 14,
  },
  timeCard: {
    flex: 1,
    marginLeft: 8,
  },
  timeContent: {
    justifyContent: 'center',
    height: '100%',
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeIcon: {
    marginRight: 8,
  },
  time: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  nextTask: {
    color: '#6E6E8E',
    fontSize: 12,
  },
  nextTaskLabel: {
    color: '#0EF7F7',
  },
  widgetsRow: {
    flexDirection: 'column',
  },
});

export default DashboardView;
