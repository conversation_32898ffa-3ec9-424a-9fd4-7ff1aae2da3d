import React from 'react';
import { StyleSheet, View, Text, ScrollView } from 'react-native';
import { useAppContext } from '../../context/AppContext';
import { Feather } from '@expo/vector-icons';
import Card from '../UI/Card';
import StatusWidgets from './StatusWidgets';
import SmartDevices from './SmartDevices';
import AppLauncher from './AppLauncher';

const DashboardView = () => {
  const { userProfile, tasks } = useAppContext();
  
  // Get current time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };
  
  // Get next upcoming task
  const getNextTask = () => {
    const incompleteTasks = tasks.filter(task => !task.completed && task.dueDate);
    if (incompleteTasks.length === 0) return null;
    
    return incompleteTasks.sort((a, b) => {
      const dateA = a.dueDate ? new Date(a.dueDate).getTime() : Infinity;
      const dateB = b.dueDate ? new Date(b.dueDate).getTime() : Infinity;
      return dateA - dateB;
    })[0];
  };
  
  const nextTask = getNextTask();
  
  // Format time
  const formatTime = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.headerCards}>
          <Card style={styles.greetingCard}>
            <View style={styles.greetingContent}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>{userProfile.name.charAt(0)}</Text>
              </View>
              <View>
                <Text style={styles.greeting}>
                  {getGreeting()}, {userProfile.name}
                </Text>
                <Text style={styles.date}>
                  {new Date().toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </Text>
              </View>
            </View>
          </Card>
          
          <Card style={styles.timeCard}>
            <View style={styles.timeContent}>
              <View style={styles.timeRow}>
                <Feather name="clock" size={24} color="#0EF7F7" style={styles.timeIcon} />
                <Text style={styles.time}>
                  {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
              </View>
              {nextTask && (
                <Text style={styles.nextTask}>
                  <Text style={styles.nextTaskLabel}>Next:</Text> {nextTask.title} at {formatTime(nextTask.dueDate)}
                </Text>
              )}
            </View>
          </Card>
        </View>
        
        <StatusWidgets />
        
        <View style={styles.widgetsRow}>
          <SmartDevices />
          <AppLauncher />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0A0A18',
  },
  content: {
    padding: 16,
  },
  headerCards: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  greetingCard: {
    flex: 2,
    marginRight: 8,
  },
  greetingContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#0BC5C5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  greeting: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  date: {
    color: '#6E6E8E',
    fontSize: 14,
  },
  timeCard: {
    flex: 1,
    marginLeft: 8,
  },
  timeContent: {
    justifyContent: 'center',
    height: '100%',
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeIcon: {
    marginRight: 8,
  },
  time: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  nextTask: {
    color: '#6E6E8E',
    fontSize: 12,
  },
  nextTaskLabel: {
    color: '#0EF7F7',
  },
  widgetsRow: {
    flexDirection: 'column',
  },
});

export default DashboardView;
