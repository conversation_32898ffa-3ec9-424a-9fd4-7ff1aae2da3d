from django.db import models
from users.models import User

class Course(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField()
    thumbnail = models.ImageField(upload_to='courses/thumbnails/')
    language = models.CharField(max_length=50)
    grade = models.CharField(max_length=10)
    subject = models.CharField(max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_offline_available = models.BooleanField(default=False)
    download_size = models.CharField(max_length=20)

    class Meta:
        db_table = 'courses'

class Module(models.Model):
    course = models.ForeignKey(Course, related_name='modules', on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    type = models.CharField(max_length=20)
    content = models.FileField(upload_to='courses/content/')
    duration = models.CharField(max_length=20)
    order = models.IntegerField()

    class Meta:
        db_table = 'modules'
        ordering = ['order']

class UserCourseProgress(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    module = models.ForeignKey(Module, on_delete=models.CASCADE)
    status = models.CharField(max_length=20)
    progress = models.IntegerField(default=0)
    last_accessed = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_course_progress'