import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useAppContext } from '../context/AppContext';
import { ViewType } from '../types';

// Components
import Sidebar from './Sidebar';
import Header from './Header';
import DashboardView from './Dashboard/DashboardView';
import ChatInterface from './Chat/ChatInterface';
import TaskList from './Tasks/TaskList';
import FilesView from './Files/FilesView';
import SettingsView from './Settings/SettingsView';
import LoginScreen from './LoginScreen';

const MainLayout = () => {
  const { activeView, isAuthenticated } = useAppContext();
  
  // Render login screen if not authenticated
  if (!isAuthenticated) {
    return <LoginScreen />;
  }
  
  const renderActiveView = () => {
    switch (activeView) {
      case ViewType.DASHBOARD:
        return <DashboardView />;
      case ViewType.CHAT:
        return <ChatInterface />;
      case ViewType.TASKS:
        return <TaskList />;
      case ViewType.FILES:
        return <FilesView />;
      case ViewType.SETTINGS:
        return <SettingsView />;
      default:
        return <DashboardView />;
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Header />
        <View style={styles.mainContent}>
          {renderActiveView()}
        </View>
      </View>
      <Sidebar />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
  },
  content: {
    flex: 1,
    flexDirection: 'column',
  },
  mainContent: {
    flex: 1,
  },
});

export default MainLayout;
