import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { useAppContext } from '../../context/AppContext';
import { Feather } from '@expo/vector-icons';
import Card from '../UI/Card';

const AppLauncher = () => {
  const { appShortcuts, launchApp } = useAppContext();

  const getIconName = (iconName) => {
    // Convert kebab-case to camelCase for Feather icons
    return iconName.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
  };

  return (
    <Card title="Quick Launch">
      <View style={styles.appGrid}>
        {appShortcuts.map((app) => (
          <TouchableOpacity
            key={app.id}
            style={styles.appItem}
            onPress={() => launchApp(app.id)}
          >
            <View style={styles.appIcon}>
              <Feather name={getIconName(app.icon)} size={24} color="#FFFFFF" />
            </View>
            <Text style={styles.appName}>{app.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  appGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  appItem: {
    width: '30%',
    alignItems: 'center',
    marginBottom: 16,
  },
  appIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#1D1D3B',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  appName: {
    color: '#FFFFFF',
    fontSize: 12,
    textAlign: 'center',
  },
});

export default AppLauncher;
