import React from 'react';
import { Download, Check, Clock, X } from 'lucide-react';
import { Course } from '../../types';

const DownloadableCourses: React.FC = () => {
  const [courses, setCourses] = React.useState<Course[]>([
    {
      id: '1',
      title: 'Mathematics Grade 5',
      description: 'Learn basic arithmetic, fractions, and decimals',
      thumbnailUrl: 'https://images.pexels.com/photos/3059654/pexels-photo-3059654.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      language: 'English',
      grade: '5',
      subject: 'Mathematics',
      modules: [],
      downloadStatus: 'not-downloaded',
      downloadSize: '25 MB'
    },
    {
      id: '2',
      title: 'Science Grade 5',
      description: 'Explore plants, animals, and simple machines',
      thumbnailUrl: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      language: 'English',
      grade: '5',
      subject: 'Science',
      modules: [],
      downloadStatus: 'downloading',
      downloadSize: '40 MB'
    },
    {
      id: '3',
      title: 'English Grade 5',
      description: 'Improve reading, writing, and comprehension',
      thumbnailUrl: 'https://images.pexels.com/photos/256417/pexels-photo-256417.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      language: 'English',
      grade: '5',
      subject: 'English',
      modules: [],
      downloadStatus: 'downloaded',
      downloadSize: '15 MB'
    }
  ]);

  const handleDownload = (courseId: string) => {
    setCourses(courses.map(course => 
      course.id === courseId 
        ? { ...course, downloadStatus: 'downloading' }
        : course
    ));

    // Simulate download completion after 3 seconds
    setTimeout(() => {
      setCourses(courses.map(course => 
        course.id === courseId 
          ? { ...course, downloadStatus: 'downloaded' }
          : course
      ));
    }, 3000);
  };

  const handleCancel = (courseId: string) => {
    setCourses(courses.map(course => 
      course.id === courseId 
        ? { ...course, downloadStatus: 'not-downloaded' }
        : course
    ));
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'not-downloaded':
        return <Download size={16} />;
      case 'downloading':
        return <Clock size={16} className="animate-pulse" />;
      case 'downloaded':
        return <Check size={16} />;
      default:
        return <Download size={16} />;
    }
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'not-downloaded':
        return 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200';
      case 'downloading':
        return 'bg-warning-100 text-warning-700';
      case 'downloaded':
        return 'bg-success-100 text-success-700';
      default:
        return 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'not-downloaded':
        return 'Download';
      case 'downloading':
        return 'Downloading...';
      case 'downloaded':
        return 'Downloaded';
      default:
        return 'Download';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-soft overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 className="text-lg font-medium text-gray-800">Available Offline Courses</h3>
      </div>
      
      <div className="divide-y divide-gray-200">
        {courses.map((course) => (
          <div key={course.id} className="px-6 py-4">
            <div className="flex items-center">
              <div className="h-12 w-12 rounded overflow-hidden flex-shrink-0">
                <img 
                  src={course.thumbnailUrl} 
                  alt={course.title} 
                  className="h-full w-full object-cover"
                />
              </div>
              
              <div className="ml-4 flex-1">
                <h4 className="text-sm font-medium text-gray-900">{course.title}</h4>
                <p className="text-xs text-gray-500 mt-1">{course.subject} · {course.downloadSize}</p>
              </div>
              
              <div className="ml-4 flex items-center">
                {course.downloadStatus === 'downloading' && (
                  <button
                    onClick={() => handleCancel(course.id)}
                    className="p-1 text-gray-400 hover:text-error-500 mr-2"
                    aria-label="Cancel download"
                  >
                    <X size={16} />
                  </button>
                )}
                <button
                  onClick={() => {
                    if (course.downloadStatus === 'not-downloaded') {
                      handleDownload(course.id);
                    }
                  }}
                  disabled={course.downloadStatus === 'downloading' || course.downloadStatus === 'downloaded'}
                  className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusClass(course.downloadStatus)} flex items-center`}
                >
                  <span className="mr-1">{getStatusIcon(course.downloadStatus)}</span>
                  {getStatusText(course.downloadStatus)}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <button className="text-sm font-medium text-primary-600 hover:text-primary-700">
          View all courses
        </button>
      </div>
    </div>
  );
};

export default DownloadableCourses;