import React from 'react';
import Card from '../UI/Card';
import { useAppContext } from '../../context/AppContext';
import * as LucideIcons from 'lucide-react';

const SmartDevices: React.FC = () => {
  const { smartDevices, toggleDeviceStatus } = useAppContext();
  
  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'light':
        return <LucideIcons.Lightbulb size={20} />;
      case 'thermostat':
        return <LucideIcons.Thermometer size={20} />;
      case 'camera':
        return <LucideIcons.Camera size={20} />;
      case 'speaker':
        return <LucideIcons.Speaker size={20} />;
      default:
        return <LucideIcons.Smartphone size={20} />;
    }
  };
  
  return (
    <Card title="Smart Devices" className="h-full">
      <div className="space-y-3">
        {smartDevices.map((device) => (
          <div 
            key={device.id}
            className="flex items-center justify-between p-2 rounded-lg border border-gray-800 bg-gray-800/30"
          >
            <div className="flex items-center">
              <div className={`p-2 rounded-full mr-3 ${device.status === 'on' ? 'bg-cyan-900/50 text-cyan-400' : 'bg-gray-800 text-gray-500'}`}>
                {getDeviceIcon(device.type)}
              </div>
              <div>
                <p className="font-medium text-white">{device.name}</p>
                <p className="text-xs text-gray-400">{device.location}</p>
              </div>
            </div>
            
            <button
              onClick={() => toggleDeviceStatus(device.id)}
              className={`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${device.status === 'on' ? 'bg-cyan-600' : 'bg-gray-700'}
              `}
            >
              <span
                className={`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${device.status === 'on' ? 'translate-x-6' : 'translate-x-1'}
                `}
              />
            </button>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default SmartDevices;