import { MessageType, TaskPriority, DeviceType, DeviceStatus, AppCategory, SkillCategory } from '../types';

export const generateDummyData = () => {
  // Messages
  const messages = [
    {
      id: '1',
      content: 'Hello BAT, what can you do for me?',
      sender: MessageType.USER,
      timestamp: new Date(Date.now() - 3600000),
    },
    {
      id: '2',
      content: "Hello <PERSON>. I'm BA<PERSON>, your personal AI assistant. I can help with tasks, control your smart home, access files, and assist with various information needs. How can I help you today?",
      sender: MessageType.ASSISTANT,
      timestamp: new Date(Date.now() - 3590000),
    },
    {
      id: '3',
      content: 'What is on my schedule today?',
      sender: MessageType.USER,
      timestamp: new Date(Date.now() - 1800000),
    },
    {
      id: '4',
      content: "You have a meeting with the development team at 2:00 PM and a doctor's appointment at 4:30 PM. Would you like me to remind you before these events?",
      sender: MessageType.ASSISTANT,
      timestamp: new Date(Date.now() - 1790000),
    },
  ];

  // Tasks
  const tasks = [
    {
      id: '1',
      title: 'Complete project proposal',
      completed: false,
      priority: TaskPriority.HIGH,
      dueDate: new Date(Date.now() + 86400000), // Tomorrow
      category: 'work',
    },
    {
      id: '2',
      title: 'Buy groceries',
      completed: false,
      priority: TaskPriority.MEDIUM,
      dueDate: new Date(Date.now() + 172800000), // Day after tomorrow
      category: 'personal',
    },
    {
      id: '3',
      title: 'Schedule dentist appointment',
      completed: true,
      priority: TaskPriority.LOW,
      category: 'health',
    },
    {
      id: '4',
      title: 'Review quarterly reports',
      completed: false,
      priority: TaskPriority.HIGH,
      dueDate: new Date(Date.now() + 259200000), // 3 days from now
      category: 'work',
    },
  ];

  // Smart Devices
  const smartDevices = [
    {
      id: '1',
      name: 'Living Room Lights',
      type: DeviceType.LIGHT,
      status: DeviceStatus.ON,
      location: 'Living Room',
    },
    {
      id: '2',
      name: 'Kitchen Thermostat',
      type: DeviceType.THERMOSTAT,
      status: DeviceStatus.ON,
      batteryLevel: 78,
      location: 'Kitchen',
    },
    {
      id: '3',
      name: 'Front Door Camera',
      type: DeviceType.CAMERA,
      status: DeviceStatus.STANDBY,
      batteryLevel: 92,
      location: 'Front Door',
    },
    {
      id: '4',
      name: 'Bedroom Speaker',
      type: DeviceType.SPEAKER,
      status: DeviceStatus.OFF,
      location: 'Bedroom',
    },
  ];

  // App Shortcuts
  const appShortcuts = [
    {
      id: '1',
      name: 'WhatsApp',
      icon: 'message-circle',
      category: AppCategory.SOCIAL,
    },
    {
      id: '2',
      name: 'Chrome',
      icon: 'globe',
      category: AppCategory.UTILITY,
    },
    {
      id: '3',
      name: 'Spotify',
      icon: 'music',
      category: AppCategory.ENTERTAINMENT,
    },
    {
      id: '4',
      name: 'Gmail',
      icon: 'mail',
      category: AppCategory.PRODUCTIVITY,
    },
    {
      id: '5',
      name: 'Calendar',
      icon: 'calendar',
      category: AppCategory.PRODUCTIVITY,
    },
    {
      id: '6',
      name: 'Files',
      icon: 'folder',
      category: AppCategory.UTILITY,
    },
  ];

  // Skills
  const skills = [
    {
      id: '1',
      name: 'Weather Forecast',
      description: 'Get weather updates',
      icon: 'cloud',
      enabled: true,
      category: SkillCategory.UTILITY,
    },
    {
      id: '2',
      name: 'News Reader',
      description: 'Read the latest news',
      icon: 'newspaper',
      enabled: true,
      category: SkillCategory.INFORMATION,
    },
    {
      id: '3',
      name: 'Smart Home Control',
      description: 'Control smart devices',
      icon: 'home',
      enabled: true,
      category: SkillCategory.UTILITY,
    },
    {
      id: '4',
      name: 'Calendar Management',
      description: 'Manage your schedule',
      icon: 'calendar',
      enabled: true,
      category: SkillCategory.PRODUCTIVITY,
    },
    {
      id: '5',
      name: 'Email Assistant',
      description: 'Help with emails',
      icon: 'mail',
      enabled: false,
      category: SkillCategory.PRODUCTIVITY,
    },
    {
      id: '6',
      name: 'Music Control',
      description: 'Control music playback',
      icon: 'music',
      enabled: false,
      category: SkillCategory.ENTERTAINMENT,
    },
    {
      id: '7',
      name: 'File Browser',
      description: 'Access and manage your files',
      icon: 'folder',
      enabled: true,
      category: SkillCategory.UTILITY,
    },
    {
      id: '8',
      name: 'Currency Converter',
      description: 'Convert between different currencies',
      icon: 'dollar-sign',
      enabled: false,
      category: SkillCategory.UTILITY,
    },
  ];

  return {
    messages,
    tasks,
    smartDevices,
    appShortcuts,
    skills,
  };
};
