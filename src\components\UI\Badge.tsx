import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  className = '',
}) => {
  const variantStyles = {
    default: 'bg-gray-800 text-gray-300',
    success: 'bg-green-900/30 text-green-400 border-green-700/50',
    warning: 'bg-yellow-900/30 text-yellow-400 border-yellow-700/50',
    error: 'bg-red-900/30 text-red-400 border-red-700/50',
    info: 'bg-blue-900/30 text-blue-400 border-blue-700/50',
  };

  return (
    <span
      className={`
        inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
        border border-gray-700/50 ${variantStyles[variant]} ${className}
      `}
    >
      {children}
    </span>
  );
};

export default Badge;