import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { useAppContext } from '../../context/AppContext';
import { Feather } from '@expo/vector-icons';
import Card from '../UI/Card';

const StatusWidgets = () => {
  const { tasks, smartDevices, isOfflineMode, isListening } = useAppContext();
  
  const activeTasks = tasks.filter((task) => !task.completed).length;
  const activeDevices = smartDevices.filter((device) => device.status === 'on').length;
  
  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <Card style={styles.widget}>
          <View style={styles.widgetContent}>
            <View style={styles.iconContainer}>
              <Feather name="check-square" size={24} color="#0EF7F7" />
            </View>
            <Text style={styles.value}>{activeTasks}</Text>
            <Text style={styles.label}>Active Tasks</Text>
          </View>
        </Card>
        
        <Card style={styles.widget}>
          <View style={styles.widgetContent}>
            <View style={styles.iconContainer}>
              <Feather name="zap" size={24} color="#0EF7F7" />
            </View>
            <Text style={styles.value}>{activeDevices}</Text>
            <Text style={styles.label}>Devices Online</Text>
          </View>
        </Card>
      </View>
      
      <View style={styles.row}>
        <Card style={styles.widget}>
          <View style={styles.widgetContent}>
            <View style={styles.iconContainer}>
              <Feather 
                name="wifi" 
                size={24} 
                color={isOfflineMode ? '#FF4D4D' : '#0EF7F7'} 
              />
            </View>
            <Text style={styles.value}>
              {isOfflineMode ? 'Offline' : 'Online'}
            </Text>
            <Text style={styles.label}>Network Status</Text>
          </View>
        </Card>
        
        <Card style={styles.widget}>
          <View style={styles.widgetContent}>
            <View style={styles.iconContainer}>
              <Feather 
                name="mic" 
                size={24} 
                color={isListening ? '#4DFF88' : '#0EF7F7'} 
              />
            </View>
            <Text style={styles.value}>
              {isListening ? 'Listening' : 'Ready'}
            </Text>
            <Text style={styles.label}>Voice Status</Text>
          </View>
        </Card>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  widget: {
    flex: 1,
    marginHorizontal: 4,
  },
  widgetContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  iconContainer: {
    marginBottom: 8,
  },
  value: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  label: {
    color: '#6E6E8E',
    fontSize: 12,
  },
});

export default StatusWidgets;
