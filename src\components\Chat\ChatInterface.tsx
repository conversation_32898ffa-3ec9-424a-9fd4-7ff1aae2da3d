import React, { useState, useRef, useEffect } from 'react';
import { useAppContext } from '../../context/AppContext';
import MessageItem from './MessageItem';
import Button from '../UI/Button';
import VoiceWaveform from '../UI/VoiceWaveform';
import * as LucideIcons from 'lucide-react';

const ChatInterface: React.FC = () => {
  const { messages, addMessage, isListening, toggleListening, isProcessing } = useAppContext();
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && !isProcessing) {
      addMessage(inputValue, 'user');
      setInputValue('');
    }
  };
  
  // Auto-scroll to bottom on new messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  
  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-800">
        <h2 className="text-xl font-semibold text-cyan-400">
          <LucideIcons.MessageCircle className="inline mr-2" size={20} />
          Conversation
        </h2>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-900">
        {messages.map((message) => (
          <MessageItem key={message.id} message={message} />
        ))}
        
        {isProcessing && (
          <div className="flex justify-start mb-4">
            <div className="h-8 w-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center shadow-glow-cyan mr-2">
              <span className="text-xs font-bold text-white">BAT</span>
            </div>
            <div className="max-w-[80%] px-4 py-2 rounded-xl bg-gray-800 border border-gray-700">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-cyan-400 animate-pulse"></div>
                <div className="w-2 h-2 rounded-full bg-cyan-400 animate-pulse delay-100"></div>
                <div className="w-2 h-2 rounded-full bg-cyan-400 animate-pulse delay-200"></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>
      
      <div className="p-4 border-t border-gray-800 bg-gray-900/50">
        {isListening && (
          <div className="mb-4 h-16 bg-gray-800 rounded-lg p-2 border border-cyan-900/50">
            <VoiceWaveform isActive={isListening} />
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="flex items-center space-x-2">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Type your message..."
            className="flex-1 bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-500"
            disabled={isProcessing}
          />
          <Button
            variant="primary"
            type="submit"
            disabled={!inputValue.trim() || isProcessing}
            className="h-10 w-10 p-0 flex items-center justify-center"
          >
            <LucideIcons.Send size={18} />
          </Button>
          <Button
            variant={isListening ? 'danger' : 'secondary'}
            onClick={toggleListening}
            className="h-10 w-10 p-0 flex items-center justify-center"
            disabled={isProcessing}
          >
            <LucideIcons.Mic size={18} />
          </Button>
        </form>
      </div>
    </div>
  );
};

export default ChatInterface;