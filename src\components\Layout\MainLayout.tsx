import React from 'react';
import Sidebar from './Sidebar';
import Header from './Header';
import { useAppContext } from '../../context/AppContext';

// Views
import DashboardView from '../Dashboard/DashboardView';
import ChatInterface from '../Chat/ChatInterface';
import TaskList from '../Tasks/TaskList';
import SettingsView from '../Settings/SettingsView';
import FilesView from '../Files/FilesView';

const MainLayout: React.FC = () => {
  const { activeView, isAuthenticated } = useAppContext();
  
  // Render login screen if not authenticated
  if (!isAuthenticated) {
    return <div>Login Screen</div>;
  }
  
  const renderActiveView = () => {
    switch (activeView) {
      case 'dashboard':
        return <DashboardView />;
      case 'chat':
        return <ChatInterface />;
      case 'tasks':
        return <TaskList />;
      case 'files':
        return <FilesView />;
      case 'settings':
        return <SettingsView />;
      default:
        return <DashboardView />;
    }
  };
  
  return (
    <div className="h-screen flex overflow-hidden">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        <main className="flex-1 overflow-hidden">
          {renderActiveView()}
        </main>
      </div>
    </div>
  );
};

export default MainLayout;